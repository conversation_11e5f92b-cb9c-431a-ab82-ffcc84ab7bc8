package com.concise.modular.wps.param;

import lombok.Data;

@Data
public class WPSResponse {
    private WPSFile file;
    private WPSUser user;
    @Data
    public static
    class WPSFile {
        private String id;
        private String name;
        private Integer version;
        private Long size;
        private Boolean readonly;
        private String creator;
        private Long create_time;
        private String modifier;
        //最近修改时间
        private Long modify_time;
        private String download_url;
        private Integer preview_pages;
        private UserAcl user_acl;
        private Watermark watermark;
        private Attrs attrs;
        @Data
        public static
        class UserAcl {
            private Integer rename;
            private Integer history;
            private Integer copy;
            private Integer export;
            private Integer print;
            private Integer comment;
            private Integer copy_control;
        }
        @Data
        public static
        class Watermark {
            private Integer type;
            private String value;
            private String fillstyle;
            private String font;
            private Float rotate;
            private Integer horizontal;
            private Integer vertical;
        }
        @Data
        public static
        class Attrs {
            private String cachetime;
        }
    }
    @Data
    public static
    class WPSUser {
        private String id;
        private String name;
        private String permission;
        private String avatar_url;
    }
}
