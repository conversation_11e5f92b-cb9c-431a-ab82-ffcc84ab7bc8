package com.concise.modular.wps.controller;

import cn.hutool.core.util.ObjectUtil;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.wps.param.WPSResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(tags = "wps回调地址")
@RestController
@RequestMapping("/open/wps")
@Slf4j
public class WPSController {

    @Resource
    private SysFileInfoService sysFileInfoService;

    //回调接口- 1.文件保存 【在线编辑必须实现】
    @BusinessLog(title = "wps-saveFileInfo", opType = LogAnnotionOpTypeEnum.EDIT)
    @PostMapping("/fileSave")
    public Object fileSave(HttpServletRequest httpServletRequest, @RequestPart("file") MultipartFile file) {
        String token = httpServletRequest.getHeader("X-Onlinedoc-Token");
        String fileId = httpServletRequest.getHeader("X-Onlinedoc-File-Id");
        String saveType = httpServletRequest.getHeader("X-Onlinedoc-SaveType");
        String zzdId = httpServletRequest.getParameter("_w_third_zzdId");
        WPSResponse response = new WPSResponse();
        SysFileInfo sysFileInfo = sysFileInfoService.uploadWPSFile(file, Long.parseLong(fileId), zzdId);
        WPSResponse.WPSFile wpsFile = new WPSResponse.WPSFile();
        wpsFile.setId(String.valueOf(sysFileInfo.getId()));
        wpsFile.setName(sysFileInfo.getFileOriginName());
        wpsFile.setVersion(sysFileInfo.getVersion());
        wpsFile.setSize(sysFileInfo.getFileSizeKb() * 1024);
        wpsFile.setDownload_url(sysFileInfo.getFilePath());
        response.setFile(wpsFile);
        return response;
    }

    //回调接口-2.文件重命名【在线编辑必须实现？】
    @PostMapping("/fileRename")
    @BusinessLog(title = "wps-fileRename", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData fileRename(HttpServletRequest httpServletRequest) {
        return new SuccessResponseData();
    }

    //回调接口-3.文件所有历史版本数据
    @PostMapping("/fileHistory")
    @BusinessLog(title = "wps-fileHistory", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData fileHistory(HttpServletRequest httpServletRequest) {
        return new SuccessResponseData();
    }

    //回调接口-4.批量获取用户信息 【在线编辑必须实现】
    @PostMapping("/userinfo")
    @BusinessLog(title = "wps-userinfo", opType = LogAnnotionOpTypeEnum.QUERY)
    public Object userinfo(HttpServletRequest httpServletRequest) {
        String token = httpServletRequest.getHeader("X-Onlinedoc-Token");
        String fileId = httpServletRequest.getHeader("X-Onlinedoc-File-Id");
        String zzdId = httpServletRequest.getParameter("_w_third_zzdId");
        WPSResponse.WPSUser wpsUser = new WPSResponse.WPSUser();
        wpsUser.setId("111");
        wpsUser.setName("编辑用户");
        WPSResponse response = new WPSResponse();
        response.setUser(wpsUser);
        return response;
    }

    //回调接口-5.回调通知
    @PostMapping("/onNotify")
    @BusinessLog(title = "wps-onNotify", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData onNotify(HttpServletRequest httpServletRequest) {
        return new SuccessResponseData();
    }

    //回调接口-6.通知当前文档写作者的用户信息
    @PostMapping("/fileOnline")
    @BusinessLog(title = "wps-fileOnline", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData fileOnline(HttpServletRequest httpServletRequest) {
        return new SuccessResponseData();
    }

    //回调接口-7.获取文件特定版本数据
    @GetMapping("/fileVersion")
    @BusinessLog(title = "wps-fileVersion", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData fileVersion(HttpServletRequest httpServletRequest) {
        return new SuccessResponseData();
    }

    //回调接口- 8.获取文件信息 【在线编辑必须实现】
    @GetMapping("/fileInfo")
    @BusinessLog(title = "wps-getFileInfo", opType = LogAnnotionOpTypeEnum.QUERY)
    public WPSResponse fileInfo(HttpServletRequest httpServletRequest) {
        String token = httpServletRequest.getHeader("X-Onlinedoc-Token");
        String fileId = httpServletRequest.getHeader("X-Onlinedoc-File-Id");

        String sfid = httpServletRequest.getParameter("_w_third_zzdId");
        String name = httpServletRequest.getParameter("_w_third_realName");
        SysFileInfo fileInfo = sysFileInfoService.getById(fileId);
        //文件信息
        WPSResponse wpsResponse = new WPSResponse();
        WPSResponse.WPSFile wpsFile = new WPSResponse.WPSFile();
        wpsFile.setModify_time(fileInfo.getUpdateTime().getTime() / 1000);
        wpsFile.setCreator(fileInfo.getCreateUser());
        wpsFile.setSize(fileInfo.getFileSizeKb() * 1024);
        wpsFile.setCreate_time(fileInfo.getCreateTime().getTime() / 1000);
        wpsFile.setModifier(ObjectUtil.isEmpty(fileInfo.getUpdateUser()) ? fileInfo.getCreateUser() : fileInfo.getUpdateUser());
        wpsFile.setName(fileInfo.getFileOriginName());
        wpsFile.setDownload_url(fileInfo.getFilePath());
        wpsFile.setPreview_pages(1);
        wpsFile.setId(fileId);
        wpsFile.setVersion(fileInfo.getVersion());
        //用户权限信息
        WPSResponse.WPSFile.UserAcl userAcl = new WPSResponse.WPSFile.UserAcl();
        userAcl.setRename(0);
        userAcl.setHistory(1);
        userAcl.setCopy(1);
        userAcl.setExport(1);
        userAcl.setPrint(1);
        userAcl.setComment(0);
        userAcl.setCopy_control(0);
        wpsFile.setUser_acl(userAcl);
        //水印参数
        WPSResponse.WPSFile.Watermark watermark = new WPSResponse.WPSFile.Watermark();
        watermark.setType(0);
        watermark.setValue("调查评估");
        watermark.setFillstyle("rgba( 192, 192, 192, 0.6 )");
        watermark.setFont("bold 20px Serif");
        watermark.setRotate((float) -0.7853982);
        watermark.setHorizontal(50);
        watermark.setVertical(50);
        wpsFile.setWatermark(watermark);

        wpsResponse.setFile(wpsFile);

        //用户信息
        WPSResponse.WPSUser wpsUser = new WPSResponse.WPSUser();
        wpsUser.setId(sfid);
        wpsUser.setName(name);
        wpsUser.setPermission("write");
        wpsUser.setAvatar_url("https://dzjz.zjsft.gov.cn/oss/zjgz/dataCollaborationTest/202505/36107b8f40ca461e9f2fa959211c94cf.jpg");
        wpsResponse.setUser(wpsUser);
        return wpsResponse;
    }
}
