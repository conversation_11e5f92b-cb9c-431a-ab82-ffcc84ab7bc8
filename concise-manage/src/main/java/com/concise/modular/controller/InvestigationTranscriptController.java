package com.concise.modular.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.common.util.DateUtil;
import com.concise.gen.investigation.entity.*;
import com.concise.gen.investigation.service.InvestigationGroupService;
import com.concise.gen.investigation.service.InvestigationInfoService;
import com.concise.gen.investigation.service.InvestigationTranscriptService;
import com.concise.gen.investigationinfoflow.entity.InvestigationInfoFlow;
import com.concise.gen.investigationinfoflow.service.InvestigationInfoFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 调查评估_笔录控制器
 *
 * <AUTHOR>
 * @date 2025-03-20 10:30:38
 */
@Slf4j
@Api(tags = "调查评估_笔录控制器")
@RestController
public class InvestigationTranscriptController {


    @Resource
    private InvestigationInfoService investigationInfoService;

    @Resource
    private InvestigationInfoFlowService investigationInfoFlowService;

    @Resource
    private InvestigationTranscriptService investigationTranscriptService;

    @Resource
    private InvestigationGroupService investigationGroupService;

    /**
     * 调查评估的【管理评估清单】、【查看和提交评估结果】 增加上一步操作
     * @param id  调查评估的id
     * @param stepCode 上一步的code 【查看和提交评估结果】传PGZT03_2  【管理评估清单】传PGZT03_1
     * @return
     */
    @GetMapping("/investigationTranscript/previousStep")
    @ApiOperation("调查评估的【管理评估清单】、【查看和提交评估结果】 增加上一步操作")
    @BusinessLog(title = "调查评估_上一步", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData previousStep(String id, String stepCode) {
        //修改主表tag字段
        investigationInfoService.lambdaUpdate()
                .set(InvestigationInfo::getTag, "PGZT03_1".equals(stepCode) ? 0 : 1)
                .eq(InvestigationInfo::getId, id)
                .update();
        //修改流程表的信息
        investigationInfoFlowService.lambdaUpdate()
                .set(InvestigationInfoFlow::getType, 9)
                .eq(InvestigationInfoFlow::getInvestigationInfoId, id)
                .eq(InvestigationInfoFlow::getStepCode, stepCode)
                .update();
        //修改流程表的spTime
        if ("PGZT03_2".equals(stepCode)) {
            investigationInfoFlowService.lambdaUpdate()
                    .set(InvestigationInfoFlow::getType, 9)
                    .set(InvestigationInfoFlow::getSpTime, DateUtil.addOneMinute(DateUtil.getDay(200), 70))
                    .eq(InvestigationInfoFlow::getInvestigationInfoId, id)
                    .eq(InvestigationInfoFlow::getStepCode, "PGZT03_3")
                    .update();
        } else {
            investigationInfoFlowService.lambdaUpdate()
                    .set(InvestigationInfoFlow::getType, 9)
                    .set(InvestigationInfoFlow::getSpTime, DateUtil.addOneMinute(DateUtil.getDay(200), 70))
                    .eq(InvestigationInfoFlow::getInvestigationInfoId, id)
                    .eq(InvestigationInfoFlow::getStepCode, "PGZT03_2")
                    .update();
            investigationInfoFlowService.lambdaUpdate()
                    .set(InvestigationInfoFlow::getType, 9)
                    .set(InvestigationInfoFlow::getSpTime, DateUtil.addOneMinute(DateUtil.getDay(200), 80))
                    .eq(InvestigationInfoFlow::getInvestigationInfoId, id)
                    .eq(InvestigationInfoFlow::getStepCode, "PGZT03_3")
                    .update();
        }
        return new SuccessResponseData();
    }

    /**
     * 查看和提交评估结果 如果是系统填写的返回表单信息
     * @param id  调查评估的id
     * @return
     */
    @GetMapping("/investigationTranscript/getPgForm")
    @ApiOperation("查看和提交评估结果 如果是系统填写的返回表单信息")
    public ResponseData getPgForm(String id) {
        // 系统填写
        Map<String, Object> map;
        InvestigationInfo inve = investigationInfoService.getById(id);
        List<InvestigationTranscript> transcriptList = investigationTranscriptService.lambdaQuery()
                .select(InvestigationTranscript::getId, InvestigationTranscript::getContext, InvestigationTranscript::getBasicInfo, InvestigationTranscript::getPaperType)
                .eq(InvestigationTranscript::getPid, id).eq(InvestigationTranscript::getDeleted, 0).eq(InvestigationTranscript::getTag,1).list();
        Map<String, String> topicMap = transcriptList.stream().flatMap(this::parseTopicStream)
                .filter(JSONObject.class::isInstance)
                .map(JSONObject.class::cast)
                .filter(topic -> ObjectUtil.isNotEmpty(topic.getString("topicType")))
                .map(this::processItemList)
                .collect(Collectors.toMap(
                        topic -> topic.getString("topicType"),
                        topic -> topic.getString("userAnswer"),
                        (existing, newValue) -> existing + SymbolConstant.SEMICOLON + newValue));
        map = new HashMap<>(topicMap);
        HashMap<String, String> personalInfoMap = new HashMap<>(4);
        transcriptList.stream().filter(transcript -> "BLLX06".equals(transcript.getPaperType()))
                .findFirst().flatMap(transcript -> Optional.ofNullable(JSON.parseObject(transcript.getBasicInfo())))
                .map(basicInfo -> basicInfo.getJSONObject("personalInfo"))
                .ifPresent(personalInfo -> {
                    personalInfoMap.put("zy", personalInfo.getString("workUnit"));
                    personalInfoMap.put("nation", personalInfo.getString("nationality"));
                    personalInfoMap.put("contactTel", personalInfo.getString("contactPhone"));
                    personalInfoMap.put("usedName", personalInfo.getString("formerName"));
                });
        map.putAll(personalInfoMap);

        map.put("correctionObjName", inve.getCorrectionObjName());
        map.put("nickName", inve.getNickName());
        return new SuccessResponseData(map);
    }

    private Stream<?> parseTopicStream(InvestigationTranscript transcript) {
        try {
            JSONObject context = JSON.parseObject(transcript.getContext());
            return context.getJSONArray("topicList").stream();
        } catch (Exception e) {
            log.error("解析调查记录异常 transcriptId: {}", transcript.getId(), e);
            return Stream.empty();
        }
    }

    private JSONObject processItemList(JSONObject topic) {
        return Optional.ofNullable(topic.getJSONArray("itemList")).map(itemList -> itemList.stream().filter(o -> {
            JSONObject item = (JSONObject) o;
            return item.getString("id").equals(topic.getString("userSelectId"));
        }).map(o -> {
            JSONObject item = (JSONObject) o;
            JSONObject answerWithContent = new JSONObject();
            answerWithContent.put("topicType", topic.getString("topicType"));
            answerWithContent.put("userAnswer", item.getString("content"));
            return answerWithContent;
        }).findFirst().orElse(topic)).orElse(topic);
    }

    /**
     * 调查评估的小组公告 增加上一步操作
     * @param id  调查评估的id
     * @return
     */
    @GetMapping("/investigationTranscript/stepToReveive")
    @ApiOperation("调查评估的小组公告 增加上一步操作")
    @BusinessLog(title = "调查评估的小组公告 增加上一步操作", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData previousStep(String id) {
        //修改主表status_字段
        investigationInfoService.lambdaUpdate()
                .set(InvestigationInfo::getStatus, "PGZT01")
                .eq(InvestigationInfo::getId, id)
                .update();
        //移除小组公告未开始的
        investigationInfoFlowService.lambdaUpdate()
                .eq(InvestigationInfoFlow::getType, 9)
                .eq(InvestigationInfoFlow::getInvestigationInfoId, id)
                .eq(InvestigationInfoFlow::getStepCode, "PGZT02")
                .remove();
        //新增流程表的信息
        List<InvestigationInfoFlow> list = Arrays.asList(
                new InvestigationInfoFlow(id, 1, 9, "PGZT01", "接收调查评估委托", DateUtil.addOneMinute(DateUtil.getDay(150), 70)),
                new InvestigationInfoFlow(id, 1, 9, "PGZT02", "调查小组公告", DateUtil.addOneMinute(DateUtil.getDay(150), 80))
        );
        investigationInfoFlowService.saveBatch(list);
        return new SuccessResponseData();
    }
}
