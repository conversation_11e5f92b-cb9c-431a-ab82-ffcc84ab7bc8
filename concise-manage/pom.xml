<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.integralN</groupId>
        <artifactId>concise</artifactId>
        <version>1.6.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>concise-manage</artifactId>
    <packaging>jar</packaging>
    <dependencies>

        <dependency>
            <groupId>com.integralN</groupId>
            <artifactId>concise-system</artifactId>
            <version>1.6.0</version>
        </dependency>

        <!-- 代码生成模块 -->
        <dependency>
            <groupId>com.integralN</groupId>
            <artifactId>concise-gen</artifactId>
            <version>1.6.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.10.5</version>
        </dependency>
        <!-- POI Word处理依赖 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>

        <!-- Word转PDF依赖 -->
        <dependency>
            <groupId>fr.opensagres.xdocreport</groupId>
            <artifactId>fr.opensagres.poi.xwpf.converter.pdf</artifactId>
            <version>2.0.2</version>
        </dependency>

        <!-- 文件操作工具类 -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>


        <dependency>
            <groupId>com.alibaba.xxpt.gateway.shared</groupId>
            <artifactId>zwdd-sdk-java-1.2.0</artifactId>
            <version>1.2.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/zwdd-sdk-java-1.2.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.pubinfo</groupId>
            <artifactId>sso-client</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/sso-client-1.0.jar</systemPath>
        </dependency>
    </dependencies>

    <build>
        <finalName>data_collaboration</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
