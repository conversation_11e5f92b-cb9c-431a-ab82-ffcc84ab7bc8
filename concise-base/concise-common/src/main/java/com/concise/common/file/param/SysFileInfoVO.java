package com.concise.common.file.param;

import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.util.OssSignedUrlUtil;
import lombok.Data;

/**
 * <p>
 * 文件信息表
 * </p>
 *
 * <AUTHOR>
 * @date 2020/6/7 22:15
 */
@Data
public class SysFileInfoVO{

    /**
     * 主键id
     */
    private String id;

    /**
     * 关联业务id
     */
    private String bizId;
    /**
     * 关联业务类别
     */
    private String bizType;

    /**
     * 类型： 1：表示页面手动上传的，空则是生成的
     */
    private String type;

    /**
     * 是否有电子签章： 1：表示有电子签章，空则表示无签章
     */
    private String signType;

    /**
     * 文件存储位置（1:阿里云，2:腾讯云，3:minio，4:本地）
     */
    private Integer fileLocation;

    /**
     * 文件仓库
     */
    private String fileBucket;

    /**
     * 文件名称（上传时候的文件名）
     */
    private String fileOriginName;

    /**
     * 文件后缀
     */
    private String fileSuffix;

    /**
     * 文件大小kb
     */
    private Long fileSizeKb;

    /**
     * 文件大小信息，计算后的
     */
    private String fileSizeInfo;

    /**
     * 存储到bucket的名称（文件唯一标识id）
     */
    private String fileObjectName;

    /**
     * 存储路径
     */
    private String filePath;

    /**
     * 外部oss
     */
    private String extFilePath;

    /**
     * id(为了匹配前端取值属性)
     */
    private String uid;

    /**
     * 文件名(为了匹配前端取值属性)
     */
    private String name;

    /**
     * url(为了匹配前端取值属性)
     */
    private String url;

    /**
     * 是否删除（0：否，1：是）
     */
    private Integer delFlag;

    /**
     * 笔录id
     */
    private String blId;

    /**
     * 文件版本
     */
    private Integer version;

    public SysFileInfoVO(){}
    public SysFileInfoVO(SysFileInfo sysFileInfo){
        this.id = String.valueOf(sysFileInfo.getId());
        this.uid = this.id;
        this.name = sysFileInfo.getFileOriginName();
        this.url = OssSignedUrlUtil.generatePresignedUrlWithPublicDomain(sysFileInfo.getFilePath());
        this.bizId = sysFileInfo.getBizId();
        this.bizType = sysFileInfo.getBizType();
        this.type = sysFileInfo.getType();
        this.signType = sysFileInfo.getSignType();
        this.fileLocation = sysFileInfo.getFileLocation();
        this.fileBucket = sysFileInfo.getFileBucket();
        this.fileOriginName = sysFileInfo.getFileOriginName();
        this.fileSuffix = sysFileInfo.getFileSuffix();
        this.fileSizeKb = sysFileInfo.getFileSizeKb();
        this.fileSizeInfo = sysFileInfo.getFileSizeInfo();
        this.fileObjectName = sysFileInfo.getFileObjectName();
        this.filePath = this.url;
        this.extFilePath = sysFileInfo.getExtFilePath();
        this.delFlag = sysFileInfo.getDelFlag();
        this.blId = sysFileInfo.getBlId();
        this.version = sysFileInfo.getVersion();
    }
}
