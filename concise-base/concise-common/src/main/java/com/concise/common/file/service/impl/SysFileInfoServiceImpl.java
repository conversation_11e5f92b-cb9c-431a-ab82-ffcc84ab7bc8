package com.concise.common.file.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.unit.DataSizeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.SimplifiedObjectMeta;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.context.requestno.RequestNoContext;
import com.concise.common.exception.LibreOfficeException;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.FileOperator;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.enums.FileLocationEnum;
import com.concise.common.file.enums.SysFileInfoExceptionEnum;
import com.concise.common.file.mapper.SysFileInfoMapper;
import com.concise.common.file.param.SysFileInfoParam;
import com.concise.common.file.param.SysFileInfoVO;
import com.concise.common.file.result.SysFileInfoResult;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.file.util.DownloadUtil;
import com.concise.common.file.util.OssBootUtil;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.LibreOfficeUtil;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.concise.common.consts.SymbolConstant.COMMA;

/**
 * 文件信息表 服务实现类
 *
 * <AUTHOR>
 * @date 2020/6/7 22:15
 */
@Service
public class SysFileInfoServiceImpl extends ServiceImpl<SysFileInfoMapper, SysFileInfo> implements SysFileInfoService {

    private static final Log log = Log.get();

    @Value("${aliyun.oss.endpoint}")
    private String endPoint;
    @Value("${aliyun.oss.accessKey}")
    private String accessKeyId;
    @Value("${aliyun.oss.secretKey}")
    private String accessKeySecret;
    @Value("${aliyun.oss.bucketName}")
    private String bucketName;
    @Value("${aliyun.oss.folder}")
    private String folder;
    @Value("${aliyun.oss.publicDomain}")
    private String publicDomain;

    //  政法协同oss
    @Value("${sqjzOss.endpoint}")
    private String sqjzOssEndPoint;
    @Value("${sqjzOss.accessKey}")
    private String sqjzOssAccessKeyId;
    @Value("${sqjzOss.secretKey}")
    private String sqjzOssAccessKeySecret;
    @Value("${sqjzOss.bucketName}")
    private String sqjzOssBucketName;
    @Value("${sqjzOss.downloadFile}")
    private boolean downloadFile;


    @Autowired
    FileOperator fileOperator;

    @Override
    public PageResult<SysFileInfo> page(SysFileInfoParam sysFileInfoParam) {

        // 构造条件
        LambdaQueryWrapper<SysFileInfo> queryWrapper = new LambdaQueryWrapper<>();

        // 拼接查询条件-文件存储位置（1:阿里云，2:腾讯云，3:minio，4:本地）
        if (ObjectUtil.isNotNull(sysFileInfoParam)) {
            if (ObjectUtil.isNotEmpty(sysFileInfoParam.getFileLocation())) {
                queryWrapper.like(SysFileInfo::getFileLocation, sysFileInfoParam.getFileLocation());
            }

            // 拼接查询条件-文件仓库
            if (ObjectUtil.isNotEmpty(sysFileInfoParam.getFileBucket())) {
                queryWrapper.like(SysFileInfo::getFileBucket, sysFileInfoParam.getFileBucket());
            }

            // 拼接查询条件-文件名称（上传时候的文件名）
            if (ObjectUtil.isNotEmpty(sysFileInfoParam.getFileOriginName())) {
                queryWrapper.like(SysFileInfo::getFileOriginName, sysFileInfoParam.getFileOriginName());
            }


            if (ObjectUtil.isNotEmpty(sysFileInfoParam.getBizId())) {
                queryWrapper.like(SysFileInfo::getBizId, sysFileInfoParam.getBizId());
            }
            if (ObjectUtil.isNotEmpty(sysFileInfoParam.getBizType())) {
                queryWrapper.like(SysFileInfo::getBizType, sysFileInfoParam.getBizType());
            }
        }

        // 查询分页结果
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SysFileInfo> list(SysFileInfoParam sysFileInfoParam) {

        // 构造条件
        LambdaQueryWrapper<SysFileInfo> queryWrapper = new LambdaQueryWrapper<>();

        return this.list(queryWrapper);
    }

    @Override
    public void add(SysFileInfoParam sysFileInfoParam) {

        // 将dto转为实体
        SysFileInfo sysFileInfo = new SysFileInfo();
        BeanUtil.copyProperties(sysFileInfoParam, sysFileInfo);

        this.save(sysFileInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(SysFileInfoParam sysFileInfoParam) {

        // 查询文件的信息
        SysFileInfo sysFileInfo = this.getById(sysFileInfoParam.getId());

        // 删除文件记录
        this.removeById(sysFileInfoParam.getId());

        // 删除具体文件
        this.fileOperator.deleteFile(sysFileInfo.getFileBucket(), sysFileInfo.getFileObjectName());
    }

    @Override
    public void edit(SysFileInfoParam sysFileInfoParam) {

        // 根据id查询实体
        SysFileInfo sysFileInfo = this.querySysFileInfo(sysFileInfoParam);

        // 请求参数转化为实体
        BeanUtil.copyProperties(sysFileInfoParam, sysFileInfo);

        this.updateById(sysFileInfo);
    }

    @Override
    public SysFileInfo detail(SysFileInfoParam sysFileInfoParam) {
        return this.querySysFileInfo(sysFileInfoParam);
    }

    @Override
    public List<SysFileInfo> detail(String bizId) {
        return this.lambdaQuery().eq(SysFileInfo::getBizId, bizId).orderByDesc(SysFileInfo::getCreateTime).list();
    }

    @Override
    public List<SysFileInfoVO> getDetailByIds(String ids) {
        List<SysFileInfoVO> fileInfoList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(ids)) {
            for (String fileId : ids.split(COMMA)) {
                SysFileInfo file = this.getById(fileId);
                if (null != file) {
                    fileInfoList.add(new SysFileInfoVO(file));
                }
            }
        }
        return fileInfoList;
    }

    @Override
    public List<AcceptCorrectionDoc> getDocListByIds(String ids) {
        List<AcceptCorrectionDoc> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(ids)) {
            for (String fileId : ids.split(COMMA)) {
                // 上传外部oss
                ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
                conf.setSupportCname(false);
                OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);
                SysFileInfo file = this.getById(fileId);
                if (null != file) {
                    AcceptCorrectionDoc doc = new AcceptCorrectionDoc();
                    doc.setWs(file.getFileOriginName());
                    if (ObjectUtil.isEmpty(file.getExtFilePath())&&ObjectUtil.isNotEmpty(file.getFileObjectName())) {
                        if (downloadFile) {
                            String extUrl = file.getFileObjectName().replace(folder,"sjxt");
                            InputStream inputStream = OssBootUtil.getObject(ossClient, bucketName,file.getFileObjectName());
                            OssBootUtil.upload(inputStream, extUrl, (OSSClient) new OSSClientBuilder().build(sqjzOssEndPoint, sqjzOssAccessKeyId, sqjzOssAccessKeySecret, conf), sqjzOssBucketName);
                            file.setExtFilePath(extUrl);
                            this.updateById(file);
                        }
                    }
                    doc.setUri(file.getExtFilePath());
                    list.add(doc);
                }
            }
        }
        return list;
    }
    @Override
    public List<SysFileInfoVO> getDocList(String bizId, String bizType) {
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);
        List<SysFileInfo> list = this.lambdaQuery().eq(SysFileInfo::getBizId, bizId).eq(SysFileInfo::getBizType, bizType).list();
        list.forEach(file -> {
            if (ObjectUtil.isEmpty(file.getExtFilePath())&&ObjectUtil.isNotEmpty(file.getFileObjectName())) {
                if (downloadFile) {
                    String extUrl = file.getFileObjectName().replace(folder,"sjxt");
                    // 上传外部oss
                    InputStream inputStream = OssBootUtil.getObject(ossClient, bucketName,file.getFileObjectName());
                    OssBootUtil.upload(inputStream, extUrl, (OSSClient) new OSSClientBuilder().build(sqjzOssEndPoint, sqjzOssAccessKeyId, sqjzOssAccessKeySecret, conf), sqjzOssBucketName);
                    file.setExtFilePath(extUrl);
                    this.updateById(file);
                }
            }
        });
        return list.stream().map(SysFileInfoVO::new).collect(Collectors.toList());
    }

    @Override
    public SysFileInfo uploadFile(MultipartFile file) {

        // 生成文件的唯一id
        Long fileId = IdWorker.getId();

        // 获取文件原始名称
        String originalFilename = file.getOriginalFilename();
        // 获取文件后缀
        String fileSuffix = null;

        if (ObjectUtil.isNotEmpty(originalFilename)) {
            fileSuffix = StrUtil.subAfter(originalFilename, SymbolConstant.PERIOD, true);
        }
        // 生成文件的最终名称
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

        // 存储文件
        byte[] bytes;
        String filePath;
        try {
            bytes = file.getBytes();
            filePath = fileOperator.storageFile(bucketName, finalName, bytes);
        } catch (IOException e) {
            throw new ServiceException(SysFileInfoExceptionEnum.ERROR_FILE);
        }

        // 计算文件大小kb
        long fileSizeKb = Convert.toLong(NumberUtil.div(new BigDecimal(file.getSize()), BigDecimal.valueOf(1024))
                .setScale(0, BigDecimal.ROUND_HALF_UP));

        //计算文件大小信息
        String fileSizeInfo = FileUtil.readableFileSize(file.getSize());

        // 存储文件信息
        SysFileInfo sysFileInfo = new SysFileInfo();
        sysFileInfo.setId(fileId);
        sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
        sysFileInfo.setFileBucket(bucketName);
        sysFileInfo.setFileObjectName(finalName);
        sysFileInfo.setFileOriginName(originalFilename);
        sysFileInfo.setFileSuffix(fileSuffix);
        sysFileInfo.setFileSizeKb(fileSizeKb);
        sysFileInfo.setFilePath(filePath);
        sysFileInfo.setFileSizeInfo(fileSizeInfo);
        this.save(sysFileInfo);

        // 返回文件id
        return sysFileInfo;
    }

    @Override
    public SysFileInfoResult getFileInfoResult(Long fileId) {
        byte[] fileBytes;
        // 获取文件名
        SysFileInfo sysFileInfo = this.getById(fileId);
        if (sysFileInfo == null) {
            throw new ServiceException(SysFileInfoExceptionEnum.NOT_EXISTED_FILE);
        }
        try {
            // 返回文件字节码
            fileBytes = fileOperator.getFileBytes(bucketName, sysFileInfo.getFileObjectName());
        } catch (Exception e) {
            log.error(">>> 获取文件流异常，请求号为：{}，具体信息为：{}", RequestNoContext.get(), e.getMessage());
            throw new ServiceException(SysFileInfoExceptionEnum.FILE_STREAM_ERROR);
        }

        SysFileInfoResult sysFileInfoResult = new SysFileInfoResult();
        BeanUtil.copyProperties(sysFileInfo, sysFileInfoResult);
        sysFileInfoResult.setFileBytes(fileBytes);

        return sysFileInfoResult;
    }

    @Override
    public void assertFile(Long field) {
        SysFileInfo sysFileInfo = this.getById(field);
        if (ObjectUtil.isEmpty(sysFileInfo)) {
            throw new ServiceException(SysFileInfoExceptionEnum.NOT_EXISTED);
        }
    }

    @Override
    public void preview(SysFileInfoParam sysFileInfoParam, HttpServletResponse response) {

        byte[] fileBytes;
        //根据文件id获取文件信息结果集
        SysFileInfoResult sysFileInfoResult = this.getFileInfoResult(sysFileInfoParam.getId());
        //获取文件后缀
        String fileSuffix = sysFileInfoResult.getFileSuffix().toLowerCase();
        //获取文件字节码
        fileBytes = sysFileInfoResult.getFileBytes();
        //如果是图片类型，则直接输出
        if (LibreOfficeUtil.isPic(fileSuffix)) {
            try {
                //设置contentType
                response.setContentType(MediaType.IMAGE_JPEG_VALUE);
                //获取outputStream
                ServletOutputStream outputStream = response.getOutputStream();
                //输出
                IoUtil.write(outputStream, true, fileBytes);
            } catch (IOException e) {
                throw new ServiceException(SysFileInfoExceptionEnum.PREVIEW_ERROR_NOT_SUPPORT);
            }

        } else if (LibreOfficeUtil.isDoc(fileSuffix)) {
            try {
                //如果是文档类型，则使用libreoffice转换为pdf或html
                InputStream inputStream = IoUtil.toStream(fileBytes);

                //获取目标contentType（word和ppt和text转成pdf，excel转成html)
                String targetContentType = LibreOfficeUtil.getTargetContentTypeBySuffix(fileSuffix);

                //设置contentType
                response.setContentType(targetContentType);

                //获取outputStream
                ServletOutputStream outputStream = response.getOutputStream();

                //转换
                LibreOfficeUtil.convertToPdf(inputStream, outputStream, fileSuffix);

                //输出
                IoUtil.write(outputStream, true, fileBytes);
            } catch (IOException e) {
                log.error(">>> 预览文件异常", e.getMessage());
                throw new ServiceException(SysFileInfoExceptionEnum.PREVIEW_ERROR_NOT_SUPPORT);

            } catch (LibreOfficeException e) {
                log.error(">>> 初始化LibreOffice失败", e.getMessage());
                throw new ServiceException(SysFileInfoExceptionEnum.PREVIEW_ERROR_LIBREOFFICE);
            }

        } else {
            //否则不支持预览（暂时）
            throw new ServiceException(SysFileInfoExceptionEnum.PREVIEW_ERROR_NOT_SUPPORT);
        }
    }

    @Override
    public void download(SysFileInfoParam sysFileInfoParam, HttpServletResponse response) {
        // 获取文件信息结果集
        SysFileInfoResult sysFileInfoResult = this.getFileInfoResult(sysFileInfoParam.getId());
        String fileName = sysFileInfoResult.getFileOriginName();
        byte[] fileBytes = sysFileInfoResult.getFileBytes();
        DownloadUtil.download(fileName, fileBytes, response);
    }

    /**
     * 获取文件信息表
     *
     * <AUTHOR>
     * @date 2020/6/7 22:15
     */
    private SysFileInfo querySysFileInfo(SysFileInfoParam sysFileInfoParam) {
        SysFileInfo sysFileInfo = this.getById(sysFileInfoParam.getId());
        if (ObjectUtil.isEmpty(sysFileInfo)) {
            throw new ServiceException(SysFileInfoExceptionEnum.NOT_EXISTED);
        }
        return sysFileInfo;
    }


    @Override
    public SysFileInfo uploadFileOss(MultipartFile file, String type, String signType) {
        // 生成文件的唯一id
        Long fileId = IdWorker.getId();
        String fileName = file.getOriginalFilename();
        // 获取文件后缀
        String fileSuffix = null;
        if (ObjectUtil.isNotEmpty(fileName)) {
            fileSuffix = StrUtil.subAfter(fileName, SymbolConstant.PERIOD, true);
        }
        // 生成文件的最终名称
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

        String key = folder + "/" + DateUtil.format(DateUtil.date(), "yyyyMM") + "/" + finalName;
        String url = OssBootUtil.upload(file, key, endPoint, accessKeyId, accessKeySecret, bucketName, publicDomain);
        //计算文件大小信息
        String fileSizeInfo = FileUtil.readableFileSize(file.getSize());
        // 计算文件大小kb
        long fileSizeKb = Convert.toLong(NumberUtil.div(new BigDecimal(file.getSize()), BigDecimal.valueOf(1024))
                .setScale(0, BigDecimal.ROUND_HALF_UP));
        // 存储文件信息
        SysFileInfo sysFileInfo = new SysFileInfo();
        sysFileInfo.setId(fileId);
        sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
        sysFileInfo.setFileBucket(bucketName);
        sysFileInfo.setFileObjectName(key);
        sysFileInfo.setFileOriginName(fileName);
        sysFileInfo.setFileSuffix(fileSuffix);
        sysFileInfo.setFileSizeKb(fileSizeKb);
        sysFileInfo.setFileSizeInfo(fileSizeInfo);
        sysFileInfo.setFilePath(url);
        sysFileInfo.setType(type);
        sysFileInfo.setSignType(signType);
        sysFileInfo.setCreateTime(new Date());
        this.save(sysFileInfo);
        return sysFileInfo;
    }

    @Override
    public SysFileInfo uploadFileOss(String fileName, byte[] bytes, String type, String signType) {
        // 生成文件的唯一id
        Long fileId = IdWorker.getId();
        // 获取文件后缀
        String fileSuffix = null;
        if (ObjectUtil.isNotEmpty(fileName)) {
            fileSuffix = StrUtil.subAfter(fileName, SymbolConstant.PERIOD, true);
        }
        // 生成文件的最终名称
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

        String key = folder + "/" + DateUtil.format(DateUtil.date(), "yyyyMM") + "/" + finalName;
        String url = "https://" + bucketName + "." + endPoint + "/" + key;


        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);
        OssBootUtil.upload(new ByteArrayInputStream(bytes), key, ossClient, bucketName);
        //计算文件大小信息
        long fileSize = OssBootUtil.getFileSize(bucketName, key, ossClient);

        // 存储文件信息
        SysFileInfo sysFileInfo = new SysFileInfo();
        sysFileInfo.setId(fileId);
        sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
        sysFileInfo.setFileBucket(bucketName);
        sysFileInfo.setFileObjectName(key);
        sysFileInfo.setFileOriginName(fileName);
        sysFileInfo.setFileSuffix(fileSuffix);
        sysFileInfo.setFileSizeKb(Convert.toLong(NumberUtil.div(new BigDecimal(fileSize), BigDecimal.valueOf(1024)).setScale(0, RoundingMode.HALF_UP)));
        sysFileInfo.setFileSizeInfo(FileUtil.readableFileSize(fileSize));
        sysFileInfo.setFilePath(url);
        sysFileInfo.setType(type);
        sysFileInfo.setSignType(signType);
        sysFileInfo.setCreateTime(new Date());
        this.save(sysFileInfo);
        return sysFileInfo;
    }

    @Override
    public SysFileInfo uploadFileExtOss(MultipartFile file) {
        // 生成文件的唯一id
        Long fileId = IdWorker.getId();
        String fileName = file.getOriginalFilename();
        // 获取文件后缀
        String fileSuffix = null;

        if (ObjectUtil.isNotEmpty(fileName)) {
            fileSuffix = StrUtil.subAfter(fileName, SymbolConstant.PERIOD, true);
        }
        // 生成文件的最终名称
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

        String extUrl = "sqjz/test.pdf";
        if (downloadFile) {
            extUrl = "sjxt/" + DateUtil.format(DateUtil.date(), "yyyyMM") + "/" + finalName;
            // 上传外部oss
            ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
            conf.setSupportCname(false);
            OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(sqjzOssEndPoint, sqjzOssAccessKeyId, sqjzOssAccessKeySecret, conf);
            OssBootUtil.upload(file, extUrl, ossClient, sqjzOssBucketName);
        }

        String key = folder + "/" + DateUtil.format(DateUtil.date(), "yyyyMM") + "/" + finalName;
        // 上传自有oss
        String url = OssBootUtil.upload(file, key, endPoint, accessKeyId, accessKeySecret, bucketName, publicDomain);
        //计算文件大小信息
        String fileSizeInfo = FileUtil.readableFileSize(file.getSize());
        // 计算文件大小kb
        long fileSizeKb = Convert.toLong(NumberUtil.div(new BigDecimal(file.getSize()), BigDecimal.valueOf(1024))
                .setScale(0, BigDecimal.ROUND_HALF_UP));
        // 存储文件信息
        SysFileInfo sysFileInfo = new SysFileInfo();
        sysFileInfo.setId(fileId);
        sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
        sysFileInfo.setFileBucket(bucketName);
        sysFileInfo.setFileObjectName(key);
        sysFileInfo.setFileOriginName(fileName);
        sysFileInfo.setFileSuffix(fileSuffix);
        sysFileInfo.setFileSizeKb(fileSizeKb);
        sysFileInfo.setFileSizeInfo(fileSizeInfo);
        sysFileInfo.setFilePath(url);
        sysFileInfo.setExtFilePath(extUrl);
        sysFileInfo.setCreateTime(new Date());
        sysFileInfo.setType("1");
        this.save(sysFileInfo);
        return sysFileInfo;
    }

    @Override
    public SysFileInfo uploadFileExtOss(String fileOriginName, byte[] bytes) {
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        String fileSuffix = null;
        if (ObjectUtil.isNotEmpty(fileOriginName)) {
            fileSuffix = StrUtil.subAfter(fileOriginName, SymbolConstant.PERIOD, true);
        }
        Long fileId = IdWorker.getId();
        String extUrl = "";
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;
        InputStream inputStream = new ByteArrayInputStream(bytes);
        if (downloadFile) {
            extUrl = "sjxt/" + DateUtil.format(DateUtil.date(), "yyyyMM") + "/" + finalName;
            // 上传外部oss
            OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(sqjzOssEndPoint, sqjzOssAccessKeyId, sqjzOssAccessKeySecret, conf);
            OssBootUtil.upload(inputStream, extUrl, ossClient, sqjzOssBucketName);
        }

        String key = folder + "/" + DateUtil.format(DateUtil.date(), "yyyyMM") + "/" + finalName;
        // 上传自有oss
        String url = "https://" + bucketName + "." + endPoint + "/" + key;
        OssBootUtil.upload(inputStream, key, (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf), bucketName);
        // 存储文件信息
        SysFileInfo sysFileInfo = new SysFileInfo();
        sysFileInfo.setId(fileId);
        sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
        sysFileInfo.setFileBucket(bucketName);
        sysFileInfo.setFileObjectName(key);
        sysFileInfo.setFileOriginName(fileOriginName);
        sysFileInfo.setFileSuffix(fileSuffix);

        sysFileInfo.setFilePath(url);
        sysFileInfo.setExtFilePath(extUrl);
        sysFileInfo.setCreateTime(new Date());
        this.save(sysFileInfo);
        return sysFileInfo;
    }

    @Override
    public List<SysFileInfo> uploadFileOss(List<AcceptCorrectionDocParam> acceptCorrectionDocList) {
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);
        OSSClient sqjzOssClient = (OSSClient) new OSSClientBuilder().build(sqjzOssEndPoint, sqjzOssAccessKeyId, sqjzOssAccessKeySecret, conf);

        List<SysFileInfo> sysFileInfoList = new ArrayList<>();
        for (AcceptCorrectionDocParam doc : acceptCorrectionDocList) {
            InputStream in = OssBootUtil.getObject(sqjzOssClient, sqjzOssBucketName, doc.getUri());

            Long fileId = IdWorker.getId();
            String fileSuffix = StrUtil.subAfter(doc.getUri(), SymbolConstant.PERIOD, true);
            String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;
            String key = folder + SymbolConstant.LEFT_DIVIDE + DateUtil.format(DateUtil.date(), "yyyyMM") + SymbolConstant.LEFT_DIVIDE + finalName;
            OssBootUtil.upload(in, key, ossClient, bucketName);
            SimplifiedObjectMeta meta = ossClient.getSimplifiedObjectMeta(bucketName, key);
            SysFileInfo sysFileInfo = new SysFileInfo();
            sysFileInfo.setId(fileId);
            sysFileInfo.setBizId(doc.getId());
            sysFileInfo.setBizType(doc.getBizType());
            sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
            sysFileInfo.setFileBucket(bucketName);
            sysFileInfo.setFileObjectName(key);
            sysFileInfo.setFileOriginName(doc.getWs());
            sysFileInfo.setFileSuffix(fileSuffix);
            sysFileInfo.setFileSizeKb(meta.getSize());
            sysFileInfo.setFileSizeInfo(DataSizeUtil.format(meta.getSize()));
            sysFileInfo.setFilePath(publicDomain + SymbolConstant.LEFT_DIVIDE + key);
            sysFileInfo.setCreateTime(new Date());
            this.save(sysFileInfo);
            sysFileInfoList.add(sysFileInfo);
        }
        return sysFileInfoList;
    }

    @Override
    public SysFileInfo uploadFileOss(File file, String bizId) {
        if (file == null) {
            return null;
        }
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);
        return saveToOss(ossClient, file, bizId, "", "");
    }

    @Override
    public SysFileInfo uploadTemplatedFileOss(String fileName, File file) {
        if (file == null) {
            return null;
        }
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);
        return saveToOss(ossClient, file, "", fileName, "");
    }

    @Override
    public void setBiz(String ids, String bizId, String bizType) {
        if (ObjectUtil.isEmpty(ids)) {
            return;
        }
        List<String> idList = Arrays.asList(ids.split(COMMA));
        if (idList.size() == 0) {
            return;
        }
        this.lambdaUpdate()
                .set(SysFileInfo::getBizId, bizId)
                .set(SysFileInfo::getBizType, bizType)
                .in(SysFileInfo::getId, idList)
                .update();
    }

    @Override
    public void removeBizBind(String bizId, String bizType) {
        this.lambdaUpdate()
                .set(SysFileInfo::getBizId, null)
                .set(SysFileInfo::getBizType, null)
                .eq(SysFileInfo::getBizId, bizId)
                .eq(SysFileInfo::getBizType, bizType)
                .update();
    }

    @Override
    public void setBizList(List<SysFileInfo> list, String bizId, String bizType) {
        if (null == list || list.size() == 0) {
            return;
        }
        List<Long> idList = new ArrayList<>();
        for (SysFileInfo sf : list) {
            idList.add(sf.getId());
        }
        this.lambdaUpdate()
                .set(SysFileInfo::getBizId, bizId)
                .set(SysFileInfo::getBizType, bizType)
                .in(SysFileInfo::getId, idList)
                .update();
    }

    private SysFileInfo saveToOss(OSSClient ossClient, File file, String bizId, String fileName, String bizType) {
        if (file == null) {
            return null;
        }
        if (ObjectUtil.isEmpty(fileName)) {
            fileName = file.getName();
        }
        // 获取文件后缀
        String fileSuffix = null;

        // 生成文件的唯一id
        Long fileId = IdWorker.getId();
        if (ObjectUtil.isNotEmpty(fileName)) {
            fileSuffix = StrUtil.subAfter(fileName, SymbolConstant.PERIOD, true);
        }
        // 生成文件的最终名称
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

        String url = DateUtil.format(DateUtil.date(), "yyyyMM") + "/" + finalName;
        String size = OssBootUtil.upload(file, ossClient, bucketName, folder + "/" + url);


        //计算文件大小信息
        String fileSizeInfo = FileUtil.readableFileSize(Long.parseLong(size));
        // 计算文件大小kb
        long fileSizeKb = Convert.toLong(NumberUtil.div(new BigDecimal(Long.parseLong(size)), BigDecimal.valueOf(1024))
                .setScale(0, RoundingMode.HALF_UP));

        // 存储文件信息
        SysFileInfo sysFileInfo = new SysFileInfo();
        sysFileInfo.setId(fileId);
        sysFileInfo.setBizId(bizId);
        sysFileInfo.setBizType(bizType);
        sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
        sysFileInfo.setFileBucket(bucketName);
        sysFileInfo.setFileObjectName(folder + "/" + url);
        sysFileInfo.setFileOriginName(fileName);
        sysFileInfo.setFileSuffix(fileSuffix);
        sysFileInfo.setFileSizeKb(fileSizeKb);
        sysFileInfo.setFileSizeInfo(fileSizeInfo);
        sysFileInfo.setFilePath(publicDomain + "/" + folder + "/" + url);
        sysFileInfo.setCreateTime(new Date());
        sysFileInfo.setUpdateTime(new Date());
        this.save(sysFileInfo);
        return sysFileInfo;
    }

    @Override
    public SysFileInfo uploadFileOss(String fileName, ByteArrayOutputStream baos) {
        // 生成文件的唯一id
        Long fileId = IdWorker.getId();
        // 获取文件后缀
        String fileSuffix = null;
        if (ObjectUtil.isNotEmpty(fileName)) {
            fileSuffix = StrUtil.subAfter(fileName, SymbolConstant.PERIOD, true);
        }
        // 生成文件的最终名称
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

        String key = folder + "/" + DateUtil.format(DateUtil.date(), "yyyyMM") + "/" + finalName;

        // 上传到OSS
        OSS ossClient = new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret);
        PutObjectRequest putObjectRequest = new PutObjectRequest(
                bucketName,
                key,
                new ByteArrayInputStream(baos.toByteArray()));

        ossClient.putObject(putObjectRequest);
        // 存储文件信息
        SysFileInfo sysFileInfo = new SysFileInfo();
        sysFileInfo.setId(fileId);
        sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
        sysFileInfo.setFileBucket(bucketName);
        sysFileInfo.setFileObjectName(key);
        sysFileInfo.setFileOriginName(fileName);
        sysFileInfo.setFileSuffix(fileSuffix);
        /*sysFileInfo.setFileSizeKb(fileSizeKb);
        sysFileInfo.setFileSizeInfo(fileSizeInfo);*/
        sysFileInfo.setFilePath(publicDomain + "/" + key);
        sysFileInfo.setCreateTime(new Date());
        this.save(sysFileInfo);
        return sysFileInfo;
    }

    @Override
    public List<SysFileInfoVO> getFileList(String bizId, String bizType) {
        LambdaQueryWrapper<SysFileInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysFileInfo::getBizId, bizId);
        lambdaQueryWrapper.eq(SysFileInfo::getDelFlag, 0);
        if (ObjectUtil.isNotEmpty(bizType)) {
            lambdaQueryWrapper.eq(SysFileInfo::getBizType, bizType);
        }
        if ("BLLX".equals(bizType)) {
            //笔录附件要按时间升序
            lambdaQueryWrapper.orderByAsc(SysFileInfo::getCreateTime);
        }
        List<SysFileInfo> list = this.list(lambdaQueryWrapper);
        return list.stream().map(SysFileInfoVO::new).collect(Collectors.toList());
    }

    @Override
    public InputStream download(SysFileInfo sysFileInfo) {
        if (ObjectUtil.isEmpty(sysFileInfo)) {
            throw new ServiceException(500, "文件信息不存在");
        }
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        return OssBootUtil.getObject((OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf), bucketName,sysFileInfo.getFileObjectName());
    }

    @Override
    public SysFileInfo uploadFileOss(File file, String bizId, String bizType, String fileName, String blType, String transcriptId, String type) {
        try {
            // 生成文件的唯一id
            Long fileId = IdWorker.getId();
            // 获取文件后缀
            String fileSuffix = null;
            if (ObjectUtil.isNotEmpty(fileName)) {
                fileSuffix = StrUtil.subAfter(fileName, SymbolConstant.PERIOD, true);
            }
            // 生成文件的最终名称
            String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

            String key = folder + "/" + DateUtil.format(DateUtil.date(), "yyyyMM") + "/" + finalName;
            String url = OssBootUtil.upload(file, key, endPoint, accessKeyId, accessKeySecret, bucketName, publicDomain);

            // 存储文件信息
            SysFileInfo sysFileInfo = new SysFileInfo();
            sysFileInfo.setId(fileId);
            sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
            sysFileInfo.setFileBucket(blType);
            sysFileInfo.setFileObjectName(key);
            sysFileInfo.setFileOriginName(fileName);
            sysFileInfo.setFileSuffix(fileSuffix);
            /*sysFileInfo.setFileSizeKb(fileSizeKb);
            sysFileInfo.setFileSizeInfo(fileSizeInfo);*/
            sysFileInfo.setFilePath(url);
            sysFileInfo.setType(type);
            sysFileInfo.setSignType(null);
            sysFileInfo.setBizType(bizType);
            sysFileInfo.setBizId(bizId);
            sysFileInfo.setBlId(transcriptId);
            sysFileInfo.setCreateTime(new Date());
            this.saveOrUpdate(sysFileInfo);
            if (file != null && file.exists()) {
                file.delete();
            }
            return sysFileInfo;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("生成笔录文书异常，transcriptId={}", transcriptId);
        }
        return null;
    }

    @Override
    public SysFileInfo uploadFileOss(MultipartFile file, String type, String signType, SysFileInfo fileInfo) {
        // 生成文件的唯一id
        Long fileId = IdWorker.getId();
        String fileName = file.getOriginalFilename();
        // 获取文件后缀
        String fileSuffix = null;
        if (ObjectUtil.isNotEmpty(fileName)) {
            fileSuffix = StrUtil.subAfter(fileName, SymbolConstant.PERIOD, true);
        }
        // 生成文件的最终名称
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

        String key = folder + "/" + DateUtil.format(DateUtil.date(), "yyyyMM") + "/" + finalName;
        String url = OssBootUtil.upload(file, key, endPoint, accessKeyId, accessKeySecret, bucketName, publicDomain);
        //计算文件大小信息
        String fileSizeInfo = FileUtil.readableFileSize(file.getSize());
        // 计算文件大小kb
        long fileSizeKb = Convert.toLong(NumberUtil.div(new BigDecimal(file.getSize()), BigDecimal.valueOf(1024))
                .setScale(0, BigDecimal.ROUND_HALF_UP));
        // 存储文件信息
        SysFileInfo sysFileInfo = new SysFileInfo();
        sysFileInfo.setId(fileId);
        sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
        sysFileInfo.setFileBucket(bucketName);
        sysFileInfo.setFileObjectName(key);
        sysFileInfo.setFileOriginName(fileName);
        sysFileInfo.setFileSuffix(fileSuffix);
        sysFileInfo.setFileSizeKb(fileSizeKb);
        sysFileInfo.setFileSizeInfo(fileSizeInfo);
        sysFileInfo.setFilePath(url);
        sysFileInfo.setType(type);
        sysFileInfo.setSignType(signType);
        sysFileInfo.setCreateTime(new Date());
        if (null != fileInfo && "BLLX".equals(fileInfo.getBizType())) {
            //调查笔录 特殊处理 需额外设置以下2字段
            sysFileInfo.setBlId(fileInfo.getBlId());
            sysFileInfo.setFileBucket(fileInfo.getFileBucket());
            //页面排序用
            sysFileInfo.setCreateTime(fileInfo.getCreateTime());
            sysFileInfo.setUpdateTime(new Date());
        }
        this.save(sysFileInfo);
        return sysFileInfo;
    }

    @Override
    public SysFileInfo uploadWPSFile(MultipartFile file, Long fileId, String updateUser) {
        SysFileInfo sysFileInfo = this.getById(fileId);
        sysFileInfo.setVersion(sysFileInfo.getVersion() + 1);
        // 生成文件的唯一id
        String fileName = file.getOriginalFilename();
        // 获取文件后缀
        String fileSuffix = null;
        if (ObjectUtil.isNotEmpty(fileName)) {
            fileSuffix = StrUtil.subAfter(fileName, SymbolConstant.PERIOD, true);
        }
        // 生成文件的最终名称
        String finalName = fileId + "_" + sysFileInfo.getVersion() + SymbolConstant.PERIOD + fileSuffix;

        String key = folder + "/" + DateUtil.format(DateUtil.date(), "yyyyMM") + "/" + finalName;
        String url = OssBootUtil.upload(file, key, endPoint, accessKeyId, accessKeySecret, bucketName, publicDomain);
        //计算文件大小信息
        String fileSizeInfo = FileUtil.readableFileSize(file.getSize());
        // 计算文件大小kb
        long fileSizeKb = Convert.toLong(NumberUtil.div(new BigDecimal(file.getSize()), BigDecimal.valueOf(1024))
                .setScale(0, BigDecimal.ROUND_HALF_UP));
        // 存储文件信息
        sysFileInfo.setId(fileId);
        sysFileInfo.setFileLocation(FileLocationEnum.ALIYUN.getCode());
        sysFileInfo.setFileBucket(bucketName);
        sysFileInfo.setFileObjectName(key);
        sysFileInfo.setFileOriginName(fileName);
        sysFileInfo.setFileSuffix(fileSuffix);
        sysFileInfo.setFileSizeKb(fileSizeKb);
        sysFileInfo.setFileSizeInfo(fileSizeInfo);
        sysFileInfo.setFilePath(url);
        sysFileInfo.setUpdateTime(new Date());
        sysFileInfo.setUpdateUser(updateUser);
        this.updateById(sysFileInfo);
        return sysFileInfo;
    }
}
