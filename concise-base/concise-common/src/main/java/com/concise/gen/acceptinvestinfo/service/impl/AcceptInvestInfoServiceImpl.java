package com.concise.gen.acceptinvestinfo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.param.SysFileInfoVO;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.base.enums.OrgTypeEnum;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.*;
import com.concise.gen.acceptbaseinfo.vo.StatVo;
import com.concise.gen.acceptchargesinfo.entity.AcceptChargesInfo;
import com.concise.gen.acceptchargesinfo.service.AcceptChargesInfoService;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.acceptinvestinfo.entity.AcceptInvestInfo;
import com.concise.gen.acceptinvestinfo.enums.AcceptInvestInfoExceptionEnum;
import com.concise.gen.acceptinvestinfo.mapper.AcceptInvestInfoMapper;
import com.concise.gen.acceptinvestinfo.param.AcceptInvestInfoParam;
import com.concise.gen.acceptinvestinfo.service.AcceptInvestInfoService;
import com.concise.gen.acceptreturninfo.service.AcceptReturnInfoService;
import com.concise.gen.areainfo.service.AreaInfoService;
import com.concise.gen.chargeinfo.service.ChargeInfoService;
import com.concise.gen.notice.enums.YwxtNoticeTypeEnum;
import com.concise.gen.notice.service.YwxtNoticeService;
import com.concise.gen.signaturemaintenance.entity.SignatureMaintenance;
import com.concise.gen.signaturemaintenance.service.SignatureMaintenanceService;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import com.concise.gen.utils.WdInterface;
import com.concise.gen.webservice.service.SendInvestInfoService;
import com.concise.gen.ywxtcount.service.YwxtCountService;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.property.TextAlignment;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 调查评估信息接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-22 13:34:07
 */
@Slf4j
@Service
public class AcceptInvestInfoServiceImpl extends ServiceImpl<AcceptInvestInfoMapper, AcceptInvestInfo> implements AcceptInvestInfoService {

    @Resource
    private AcceptReturnInfoService acceptReturnInfoService;
    @Resource
    private OrgCommonService orgCommonService;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private AcceptChargesInfoService acceptChargesInfoService;
    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;
    @Resource
    private YwxtNoticeService ywxtNoticeService;
    @Resource
    private YwxtCountService ywxtCountService;
    @Resource
    private SendInvestInfoService sendService;
    @Resource
    private AreaInfoService areaInfoService;
    @Resource
    private SignatureMaintenanceService signatureMaintenanceService;

    @Resource
    private ChargeInfoService chargeInfoService;

    @Resource
    private ElectronicSignatureUtil electronicSignatureUtil;

    @Value("${electronicSignature.fontPath}")
    private String fontPath;
    @Override
    public PageResult<AcceptInvestInfo> page(AcceptInvestInfoParam param) {
        QueryWrapper<AcceptInvestInfo> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {
            // 根据 查询
            if (ObjectUtil.isNotEmpty(param.getTaskId())) {
                queryWrapper.lambda().eq(AcceptInvestInfo::getTaskId, param.getTaskId());
            }
            // 根据数据来源(机构类型) 查询
            if (ObjectUtil.isNotEmpty(param.getSjlylx())) {
                queryWrapper.lambda().likeRight(AcceptInvestInfo::getSjlylx, param.getSjlylx());
            }
            if (ObjectUtil.isNotEmpty(param.getDataFrom())) {
                queryWrapper.lambda().eq(AcceptInvestInfo::getDataFrom, param.getDataFrom());
            }
            // 根据接收单位 查询
            if (ObjectUtil.isNotEmpty(param.getJsdw())) {
                queryWrapper.lambda().and(i -> i.eq(AcceptInvestInfo::getJsdwId, param.getJsdw()).or().like(AcceptInvestInfo::getJsdwPids, param.getJsdw()));
            }
            // 数据状态 查询
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().eq(AcceptInvestInfo::getZt, param.getZt());
            }
            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.lambda().ge(AcceptInvestInfo::getSdsj, param.getSearchBeginTime());
                queryWrapper.lambda().le(AcceptInvestInfo::getSdsj, param.getSearchEndTime());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getBgrxm())) {
                queryWrapper.lambda().like(AcceptInvestInfo::getBgrxm, param.getBgrxm());
            }
        }
        queryWrapper.lambda().orderByDesc(AcceptInvestInfo::getSdsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptCorrectionDocParam> add(AcceptInvestInfoParam param) {
        AcceptInvestInfo old = this.lambdaQuery()
                .eq(AcceptInvestInfo::getJsdw, param.getJsdw())
                .eq(AcceptInvestInfo::getZjhm, param.getZjhm())
                .orderByDesc(AcceptInvestInfo::getSdsj)
                .last("limit 1")
                .one();

        param.setHjszd(areaInfoService.transToAreaIds(param.getHjszd()));
        param.setZsd(areaInfoService.transToAreaIds(param.getZsd()));
        if (ObjectUtil.isEmpty(param.getWtdw())) {
            param.setWtdw(param.getTsdw());
        }
        if (ObjectUtil.isEmpty(param.getWtdwdd())) {
            param.setWtdwdd(param.getTsdwmc());
        }
        if (old != null && old.getSdsj() != null && !"2".equals(old.getZt())) {
            List<AcceptCorrectionDoc> oldDocList = acceptCorrectionDocService.list(old.getId());
            Boolean check = acceptReturnInfoService.check(old.getSdsj(), param.getDocList(), oldDocList);
            if (check) {
                if ("0".equals(old.getZt())){
                    //旧数据未接收时退回旧数据
                    acceptReturnInfoService.save(param.getXtbh(), param.getId(), param.getTyfh(), DateUtil.parse(param.getTssj()), old.getSdsj(), old.getBgrxm(), old.getJsdw(), old.getJsdwId(), old.getJsdwPids(), old.getJsdwmc());
                    old.setZt("2");
                    old.setShbz("系统识别为重复案件，自动退回");
                    old.setShjg("26");
                    old.setShjgName("其他");
                    old.setShry("数据质检");
                    old.setShsj(DateUtil.date());
                    old.setReturned("1");
                    this.updateById(old);
                    sendService.sendMessage(old,"数据质检");
                }else {
                    //自动退回
                    param.setZt("2");
                    param.setReturned("1");
                    param.setShbz("系统识别为重复案件，自动退回");
                    param.setShjg("26");
                    param.setShry("数据质检");
                    param.setShsj(DateUtil.now());
                }
            }
        }
        AcceptInvestInfo acceptInvestInfo = new AcceptInvestInfo();
        BeanUtil.copyProperties(param, acceptInvestInfo);
        if ("99".equals(acceptInvestInfo.getNsyjzlb())) {
            // 万达不接收矫正类别为其他的数据
            acceptInvestInfo.setNsyjzlb(null);
        }
        acceptInvestInfo.setCaseReason(chargeInfoService.toChinese(param.getCaseReasonList()));
        param.getDocList().forEach(doc -> acceptCorrectionDocService.add(doc));

        this.save(acceptInvestInfo);
        if ("2".equals(param.getZt())) {
            // 自动退回的消息
            sendService.sendMessage(acceptInvestInfo,"数据质检");
        }
        //发送通知
        if (ObjectUtil.isNotEmpty(param.getBgrxm())) {
            ywxtNoticeService.buildNoticeByOrgId(
                    YwxtNoticeTypeEnum.INVEST_01,
                    param.getJsdwmc()+","+DateUtil.formatDate(acceptInvestInfo.getSdsj())+","+param.getTsdwmc()+","+param.getBgrxm(),
                    param.getTsdwmc()+","+param.getBgrxm(),
                    param.getJsdwId());
        }
        //更新统计
        int count1 = this.lambdaQuery().eq(AcceptInvestInfo::getJsdw, acceptInvestInfo.getJsdw())
                .apply("date_format (sdsj,'%Y-%m-%d') = date_format('" + acceptInvestInfo.getSdsj() + "','%Y-%m-%d')")
                .count();
        OrgCommon org1 = orgCommonService.getOrgByCode(acceptInvestInfo.getJsdw());
        ywxtCountService.saveOrUpdateCount("T06", DateUtil.formatDate(acceptInvestInfo.getSdsj()), org1.getId(), count1,true);
        return param.getDocList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AcceptInvestInfoParam acceptInvestInfoParam) {
        this.removeById(acceptInvestInfoParam.getId());
    }

    @Override
    public void delete(String taskId) {

    }

    @Override
    public void edit(AcceptInvestInfoParam param) {
        AcceptInvestInfo acceptInvestInfo = this.queryAcceptInvestInfo(param);
        if (!"4".equals(param.getZt())) {
            if (param.getShjg().startsWith("1")) {
                param.setZt("1");
            }else {
                param.setZt("2");
            }
            param.setSjjzdqh(param.getZsd());
            param.setSjjzdmx(param.getZsdxxdz());
        }

        if (ObjectUtil.isNotEmpty(param.getDcdw()) && !param.getDcdw().equals(acceptInvestInfo.getDcdw())) {
            OrgCommon dcdw = orgCommonService.getById(param.getDcdw());
            OrgCommon qxdcdw = orgCommonService.getById(dcdw.getPid());
            if (qxdcdw != null) {
                param.setQxdcdw(qxdcdw.getCode());
                param.setDcdwlxr(qxdcdw.getLxr());
                param.setDcdwlxdh(qxdcdw.getLxdh());
            }
        }
        BeanUtil.copyProperties(param, acceptInvestInfo);
        if (ObjectUtil.isNotEmpty(param.getWsDckssj())) {
            acceptInvestInfo.setWsDckssj(param.getWsDckssj().substring(0,10));
            acceptInvestInfo.setWsDcjssj(param.getWsDcjssj().substring(0,10));
        }
        this.updateById(acceptInvestInfo);


        if (ObjectUtil.isNotEmpty(param.getChargesList())) {
            acceptChargesInfoService.lambdaUpdate().eq(AcceptChargesInfo::getContactId,param.getId()).remove();
            acceptChargesInfoService.saveBatch(param.getChargesList());
        }

        if ("1".equals(param.getZt())) {
            sendService.sendMessage(this.queryAcceptInvestInfo(param),param.getShry());
            // 发送万达
            AcceptInvestInfo byId = this.getById(acceptInvestInfo.getId());
            byId.setChargesList(acceptChargesInfoService.list(acceptInvestInfo.getId()));
            byId.setDocList(acceptCorrectionDocService.list(acceptInvestInfo.getId()));
            if (!sendService.sendWd(byId)) {
                throw new ServiceException(AcceptInvestInfoExceptionEnum.SYNC_ERROR);
            }
        }
        if ("4".equals(param.getZt())) {
            // 反馈
            if (!feedback(param.getId())) {
                acceptInvestInfo.setZt("1");
                this.updateById(acceptInvestInfo);
            }
        }
    }

    @Override
    public boolean feedback(String id) {
        AcceptInvestInfo feedback = this.getById(id);
        feedback.setSjjzdqh(areaInfoService.transToAreaCode(feedback.getSjjzdqh()));
        feedback.setZsd(areaInfoService.transToAreaCode(feedback.getZsd()));
        feedback.setHjszd(areaInfoService.transToAreaCode(feedback.getHjszd()));
        List<AcceptCorrectionDoc> hzclList = sysFileInfoService.getDocListByIds(feedback.getHzcl());
        if (OrgTypeEnum.JIAN_YU.getCode().equals(feedback.getSjlylx())) {
            if (!sendService.request31002_1(feedback,hzclList)) {
                return false;
            }
            feedback.setChargesList(acceptChargesInfoService.list(feedback.getId()));
            sendService.request31002_2(feedback,hzclList);
        }
        if (OrgTypeEnum.FA_YUAN.getCode().equals(feedback.getSjlylx())) {
            if (!sendService.request31004_1(feedback,hzclList)) {
                return false;
            }
            feedback.setChargesList(acceptChargesInfoService.list(feedback.getId()));
            sendService.request31002_2(feedback,hzclList);
        }
        if (OrgTypeEnum.JIAN_GUAN_CHANG_SUO.getCode().equals(feedback.getSjlylx())) {
            if (!sendService.request31006_1(feedback,hzclList)) {
                return false;
            }
            feedback.setChargesList(acceptChargesInfoService.list(feedback.getId()));
            sendService.request31002_2(feedback,hzclList);
        }
        if (OrgTypeEnum.JIAN_CHA_YUAN.getCode().equals(feedback.getSjlylx())) {
            if (!sendService.request31007_1(feedback,hzclList)) {
                return false;
            }
        }
        //更新统计
        int count1 = this.lambdaQuery().eq(AcceptInvestInfo::getJsdw, feedback.getQxdcdw())
                .apply("date_format (sdsj,'%Y-%m-%d') = date_format('" + feedback.getFksj() + "','%Y-%m-%d')")
                .count();
        OrgCommon org1 = orgCommonService.getOrgByCode(feedback.getQxdcdw());
        ywxtCountService.saveOrUpdateCount("T07", DateUtil.formatDate(feedback.getFksj()), org1.getId(), count1,true);
        return true;
    }


    @Override
    public AcceptInvestInfo detail(String id) {
        AcceptInvestInfo investInfo = this.getById(id);
        investInfo.setChargesList(acceptChargesInfoService.list(investInfo.getId()));

        if ("1".equals(investInfo.getZt())) {
            OrgCommon org = orgCommonService.getOrgByCode(investInfo.getJsdw());
            investInfo.setDcdwlxr(org.getLxr());
            investInfo.setDcdwlxdh(org.getLxdh());
        }

        if (ObjectUtil.isNotEmpty(investInfo.getHzcl())) {
            List<SysFileInfoVO> fileInfoList= new ArrayList<>();
            for (String fileId : investInfo.getHzcl().split(SymbolConstant.COMMA)) {
                SysFileInfo file = sysFileInfoService.getById(fileId);
                if (null != file) {
                    fileInfoList.add(new SysFileInfoVO(file));
                }
            }
            investInfo.setHzclList(fileInfoList);
        }
        return investInfo;
    }

    @Override
    public AcceptInvestInfo getWdResult(String id) {
        AcceptInvestInfo byId = this.getById(id);
        if (byId == null) {
            return byId;
        }
        try {
            JSONObject result = WdInterface.investigateResult(id);
            if (result.getInteger("code") == 0) {
                JSONObject data = result.getJSONArray("data").getJSONObject(0);
                byId.setWsDckssj(data.getString("dcqssj"));
                byId.setWsDcjssj(data.getString("dcjzsj"));
                byId.setWsDcygqk(data.getString("ygqk"));
                if (ObjectUtil.isNotEmpty(data.getString("pgyj"))) {
                    byId.setDcpgjl("适宜社区矫正".equalsIgnoreCase(data.getString("pgyj"))?"1":"2");
                }
                this.updateById(byId);
            }
        }catch (Exception e){
            log.error("获取万达结果失败id: {}",id);
        }
        return byId;
    }

    @Override
    public JSONArray step(AcceptInvestInfoParam param) {
        AcceptInvestInfo detail = this.getById(param.getId());
        if (detail == null) {
            return new JSONArray();
        }
        JSONArray steps = new JSONArray();
        JSONObject s1 = new JSONObject();
        s1.put("stepName","社区矫正决定机关委托");
        s1.put("operator", detail.getTsdwmc());
        s1.put("timeTitle","委托日期");
        s1.put("opTime",DateUtil.formatDate(detail.getWtsj()));
        s1.put("sysName","政法一体化");
        JSONArray sa1 = new JSONArray();
        sa1.add(s1);
        JSONObject g1 = new JSONObject();
        g1.put("groupName","委托调查");
        g1.put("icon","step1");
        g1.put("steps",sa1);
        steps.add(g1);

        OrgCommon org = orgCommonService.getOrgByCode(detail.getQxdcdw());
        String orgName = "";
        if (org != null) {
            orgName = org.getName();
        }
        if ("0".equals(detail.getZt())) {
            steps.add(JSONArray.parse("{\"groupName\":\"审核信息\",\"icon\":\"step2\",\"steps\":[{\"timeTitle\":\"审核日期\",\"stepName\":\"社区矫正机构审核并下派至调查司法所\",\"stepType\":\"1\",\"sysName\":\"社矫数据协同系统\",\"operator\":\""+ detail.getJsdwmc()+"\",\"active\":true}]}"));
            steps.add(JSONArray.parse("{\"groupName\":\"调查登记\",\"icon\":\"step3\",\"steps\":[{\"timeTitle\":\"调查日期\",\"stepName\":\"执法办案登记调查信息\"}]}"));
            steps.add(JSONArray.parse("{\"groupName\":\"调查反馈\",\"icon\":\"step4\",\"steps\":[{\"timeTitle\":\"反馈日期\",\"stepName\":\"反馈回执至委托机关及检察机关\"}]}"));
        }

        if ("1".equals(detail.getZt())) {
            JSONObject s2 = new JSONObject();
            s2.put("stepName","社区矫正机构审核并下派至调查司法所");
            s2.put("operator", detail.getJsdwmc());
            s2.put("timeTitle","审核日期");
            s2.put("opTime", DateUtil.formatDate(detail.getShsj()));
            s2.put("sysName","社矫数据协同系统");
            JSONArray sa2 = new JSONArray();
            sa2.add(s2);
            JSONObject g2 = new JSONObject();
            g2.put("groupName","审核信息");
            g2.put("icon","step2");
            g2.put("steps",sa2);
            steps.add(g2);
            JSONObject s3 = new JSONObject();
            s3.put("stepName","执法办案登记调查信息");
            s3.put("operator", orgName);
            s3.put("timeTitle","调查日期");
            s3.put("opTime", DateUtil.formatDate(detail.getRjrq()));
            s3.put("sysName","社矫执法办案系统");
            JSONArray sa3 = new JSONArray();
            sa3.add(s3);
            JSONObject g3 = new JSONObject();
            g3.put("groupName","调查登记");
            g3.put("icon","step3");
            g3.put("steps",sa3);
            steps.add(g3);
            steps.add(JSONArray.parse("{\"groupName\":\"调查反馈\",\"icon\":\"step4\",\"steps\":[{\"timeTitle\":\"反馈日期\",\"stepName\":\"反馈回执至委托机关及检察机关\",\"stepType\":\"2\",\"active\":true}]}"));
        }

        if ("2".equals(detail.getZt())||"3".equals(detail.getZt())) {
            //退回
            JSONObject s2 = new JSONObject();
            s2.put("stepName","社区矫正机构审核并下派至调查司法所");
            s2.put("active",true);
            s2.put("operator", detail.getJsdwmc());
            s2.put("timeTitle","退回日期");
            s2.put("opTime", DateUtil.formatDate(detail.getShsj()));
            s2.put("sysName","社矫数据协同系统");
            s2.put("stepType","3");
            s2.put("remark",detail.getShjgName()+","+detail.getShbz());
            JSONArray sa2 = new JSONArray();
            sa2.add(s2);
            JSONObject g2 = new JSONObject();
            g2.put("groupName","审核信息");
            g2.put("icon","step2");
            g2.put("steps",sa2);
            steps.add(g2);
        }


        if ("4".equals(detail.getZt())) {
            JSONObject s2 = new JSONObject();
            s2.put("stepName","社区矫正机构审核并下派至调查司法所");
            s2.put("operator", detail.getJsdwmc());
            s2.put("timeTitle","审核日期");
            s2.put("opTime", DateUtil.formatDate(detail.getShsj()));
            s2.put("sysName","社矫数据协同系统");
            JSONArray sa2 = new JSONArray();
            sa2.add(s2);
            JSONObject g2 = new JSONObject();
            g2.put("groupName","审核信息");
            g2.put("icon","step2");
            g2.put("steps",sa2);
            steps.add(g2);

            JSONObject s3 = new JSONObject();
            s3.put("stepName","执法办案登记调查信息");
            s3.put("operator", orgName);
            s3.put("timeTitle","调查日期");
            s3.put("opTime", DateUtil.formatDate(detail.getDcpgrq()));
            s3.put("sysName","社矫执法办案系统");
            JSONArray sa3 = new JSONArray();
            sa3.add(s3);
            JSONObject g3 = new JSONObject();
            g3.put("groupName","调查登记");
            g3.put("icon","step3");
            g3.put("steps",sa3);
            steps.add(g3);

            JSONObject s4 = new JSONObject();
            s4.put("stepName","反馈回执至委托机关及检察机关");
            s4.put("active",true);
            s4.put("operator", detail.getJsdwmc());
            s4.put("timeTitle","反馈日期");
            s4.put("opTime", DateUtil.formatDate(detail.getFksj()));
            s4.put("sysName","社矫数据协同系统");
            JSONArray sa4 = new JSONArray();
            sa4.add(s4);
            JSONObject g4 = new JSONObject();
            g4.put("groupName","调查反馈");
            g4.put("icon","step4");
            g4.put("steps",sa4);
            steps.add(g4);
        }
        return steps;
    }

    @Override
    public void getNoSignPdf(AcceptInvestInfoParam param) {
        AcceptInvestInfo model = this.getById(param.getId());
        model.setWsDckssj(param.getWsDckssj().substring(0,10));
        model.setWsDcjssj(param.getWsDcjssj().substring(0,10));
        model.setWsWtdw(param.getWsWtdw());
        model.setWsRylx(param.getWsRylx());
        model.setWsDcygqk(param.getWsDcygqk());
        model.setDcpgwsh(param.getDcpgwsh());
        this.updateById(model);
        model.setDcpgjl(param.getDcpgjl());
        HttpServletResponse response = HttpServletUtil.getResponse();
        try {
            byte[] bytes = getNoSignPdfBytes(model);
            MultipartFile multipartFile = new MockMultipartFile("fileName.pdf", bytes);
            byte[] fileBytes = multipartFile.getBytes();
            //设置contentType
            response.setContentType("application/pdf");
            //获取outputStream
            ServletOutputStream outputStream = response.getOutputStream();
            //输出
            IoUtil.write(outputStream, true, fileBytes);
            outputStream.close();
        } catch (Exception e) {
            throw new RuntimeException("获取签章pdf异常，" + e.getMessage());
        }
    }

    @Override
    public void getSignedPdf(AcceptInvestInfoParam param) {
        HttpServletResponse response = HttpServletUtil.getResponse();
        AcceptInvestInfo model = this.getById(param.getId());
        model.setWsDckssj(param.getWsDckssj().substring(0,10));
        model.setWsDcjssj(param.getWsDcjssj().substring(0,10));
        model.setWsWtdw(param.getWsWtdw());
        model.setWsRylx(param.getWsRylx());
        model.setWsDcygqk(param.getWsDcygqk());
        model.setDcpgwsh(param.getDcpgwsh());
        this.updateById(model);
        model.setDcpgjl(param.getDcpgjl());
        String sealNo;
        SignatureMaintenance signatureMaintenance = signatureMaintenanceService.lambdaQuery()
                .eq(SignatureMaintenance::getJzjg, model.getJsdwId())
                .eq(SignatureMaintenance::getSealType, param.getSealType())
                .eq(SignatureMaintenance::getEnabled, 0)
                .one();
        if (ObjectUtil.isNotEmpty(signatureMaintenance)) {
            sealNo = signatureMaintenance.getSealNo();
        } else {
            try {
                ResponseUtil.responseExceptionError(response, 500, "未维护社区矫正机构章编码，请联系技术人员或手动上传", null);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return;
        }

        try {

            String base64 = Base64.getEncoder().encodeToString(getNoSignPdfBytes(model));
            String signedBase64 = electronicSignatureUtil.signedBase64(base64, sealNo, model.getBgrxm() + "调查评估意见书.pdf", "（公章）");

            MultipartFile multipartFile = PdfFillUtil.convertBase64ToMultipartFile(signedBase64, model.getBgrxm() + "调查评估意见书.pdf");
            SysFileInfo sysFileInfo = sysFileInfoService.uploadFileOss(multipartFile, null, null);
            if (ObjectUtil.isEmpty(model.getHzcl())) {
                model.setHzcl(String.valueOf(sysFileInfo.getId()));
            }else {
                model.setHzcl(model.getHzcl()+","+sysFileInfo.getId());
            }
            this.updateById(model);

            byte[] fileBytes = multipartFile.getBytes();
            //设置contentType
            response.setContentType("application/pdf");
            //获取outputStream
            ServletOutputStream outputStream = response.getOutputStream();
            //输出
            IoUtil.write(outputStream, true, fileBytes);
            outputStream.close();

        }catch (Exception e){
            throw new RuntimeException("获取签章pdf异常，" + e.getMessage());
        }

    }

    @Override
    public List<StatVo> statistics(String orgId, int level, String start, String end) {

        if (level == 1) {
            return addSum(this.baseMapper.statisticsLevel1(start,end));
        }else if (level == 2) {
            return addSum(this.baseMapper.statisticsLevel2(orgId,start,end));
        }else if (level == 3) {
            return addSum(this.baseMapper.statisticsLevel3(orgId,start,end));
        }else if (level == 4) {
            return this.baseMapper.statisticsLevel4(orgId,start,end);
        }
        return null;
    }

    @Override
    public void statisticsExport(String orgId, int level, String start, String end) {
        List<StatVo> list = null;
        if (level == 1) {
            list = addSum(this.baseMapper.statisticsLevel1(start,end));
        }else if (level == 2) {
            list =  addSum(this.baseMapper.statisticsLevel2(orgId,start,end));
        }else if (level == 3) {
            list =  addSum(this.baseMapper.statisticsLevel3(orgId,start,end));
        }else if (level == 4) {
            list =  this.baseMapper.statisticsLevel4(orgId,start,end);
        }
        PoiUtil.exportExcelWithStream("调查评估协同统计.xls", StatVo.class, list);
    }

    private List<StatVo> addSum(List<StatVo> statVos) {
        StatVo all = new StatVo("总计");
        statVos.forEach(statVo -> {
            all.setZt1(statVo.getZt1()+all.getZt1());
            all.setZt2(statVo.getZt2()+all.getZt2());
            all.setZt3(statVo.getZt3()+all.getZt3());
            all.setZt4(statVo.getZt4()+all.getZt4());
            all.setZt5(statVo.getZt5()+all.getZt5());
        });
        statVos.add(0,all);
        return statVos;
    }


    private byte[] getNoSignPdfBytes(AcceptInvestInfo model) throws IOException {

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        PdfWriter pdfWriter=new PdfWriter(os);
        //2、创建文档对象
        PdfDocument pdfDocument=new PdfDocument(pdfWriter);
        //3、创建内容文档对象
        Document document=new Document(pdfDocument, PageSize.A4);
        //设置字体，解决中文显示问题
        PdfFont fontFs= PdfFontFactory.createFont(IOUtils.toByteArray(Files.newInputStream(Paths.get(fontPath))), "Identity-H", true);
        //创建内容
        String[] ks = model.getWsDckssj().split("-");
        String[] js = model.getWsDcjssj().split("-");

        document.add(new Paragraph("调查评估意见书").setFont(fontFs).setFontSize(21.5F).setTextAlignment(TextAlignment.CENTER));
        document.add(new Paragraph(model.getDcpgwsh()).setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.RIGHT));
        document.add(new Paragraph(model.getWsWtdw()+":").setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.LEFT).setUnderline());
        document.add(new Paragraph("受你单位委托，我局于").setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.LEFT).setFirstLineIndent(30)
                .add(new Text(ks[0]).setUnderline()).add("年").add(new Text(ks[1]).setUnderline()).add("月").add(new Text(ks[2]).setUnderline()).add("日至")
                .add(new Text(js[0]).setUnderline()).add("年").add(new Text(js[1]).setUnderline()).add("月").add(new Text(js[2]).setUnderline()).add("日对"+model.getWsRylx())
                .add(new Text(model.getBgrxm()).setUnderline())
                .add("进行了调查评估。有关情况如下:")
                .add(new Text(model.getWsDcygqk()).setUnderline())
                .add("。")
        );

        document.add(new Paragraph("综合以上情况，评估意见为").setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.LEFT).setTextAlignment(TextAlignment.LEFT).setFirstLineIndent(30).add(new Text(model.getDcpgjl()).setUnderline()).add("。"));
        document.add(new Paragraph("（公章）").setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.RIGHT).setRelativePosition(10,10,10,10));
        document.add(new Paragraph(DateUtil.format(DateUtil.date(),"yyyy年MM月dd日")).setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.RIGHT).setRelativePosition(10,10,10,10));
        document.close();

        return os.toByteArray();
    }

    /**
     * 获取调查评估信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-22 13:34:07
     */
    private AcceptInvestInfo queryAcceptInvestInfo(AcceptInvestInfoParam acceptInvestInfoParam) {
        AcceptInvestInfo acceptInvestInfo = this.getById(acceptInvestInfoParam.getId());
        if (ObjectUtil.isNull(acceptInvestInfo)) {
            throw new ServiceException(AcceptInvestInfoExceptionEnum.NOT_EXIST);
        }
        return acceptInvestInfo;
    }
}
