package com.concise.gen.investigation.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.gen.areainfo.service.AreaInfoService;
import com.concise.gen.investigation.entity.InvestigationGroup;
import com.concise.gen.investigation.entity.InvestigationInfo;
import com.concise.gen.investigation.entity.InvestigationTranscript;
import com.concise.gen.investigation.mapper.InvestigationGroupMapper;
import com.concise.gen.investigation.mapper.InvestigationInfoMapper;
import com.concise.gen.investigation.mapper.InvestigationTranscriptMapper;
import com.concise.gen.investigation.param.InvestigationRecordParam;
import com.concise.gen.investigation.param.InvestigationTranscriptParam;
import com.concise.gen.investigation.service.InvestigationGroupService;
import com.concise.gen.investigation.service.InvestigationInfoService;
import com.concise.gen.investigation.service.InvestigationTranscriptService;
import com.concise.gen.papermaintenance.entity.PaperMaintenance;
import com.concise.gen.papermaintenance.param.PaperMaintenanceParam;
import com.concise.gen.papermaintenance.service.PaperMaintenanceService;
import com.concise.gen.papertopic.param.PaperTopicParam;
import com.concise.gen.papertopicitem.param.PaperTopicItemParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 调查评估_笔录service接口实现类
 *
 * <AUTHOR>
 * @date 2025-03-24 14:59:13
 */
@Slf4j
@Service
public class InvestigationTranscriptServiceImpl extends ServiceImpl<InvestigationTranscriptMapper, InvestigationTranscript> implements InvestigationTranscriptService {

    @Resource
    private PaperMaintenanceService paperMaintenanceService;

    @Resource
    private InvestigationInfoMapper investigationInfoMapper;

    @Resource
    private InvestigationGroupMapper investigationGroupMapper;

    @Resource
    private AreaInfoService areaInfoService;

    @Resource
    private SysFileInfoService sysFileInfoService;

    @Resource
    private InvestigationGroupService investigationGroupService;

    @Resource
    private InvestigationInfoService investigationInfoService;

    @Value("${electronicSignature.bl_bgr}")
    private String bl_bgr;

    @Value("${electronicSignature.bl_bhr}")
    private String bl_bhr;

    @Override
    public void genEmptyTranscript(InvestigationInfo param) {
        if (null != param.getTag() && 2 == param.getTag()) {
            //若流程已走到查看和提交评估结果或之后的步骤，则不生成，防止手动制作笔录时，流程记录点到管理评估清单重新生成在线制作的空笔录
            return;
        }
        //如果笔录不为空，跳过
        int count = this.count(new LambdaQueryWrapper<InvestigationTranscript>().eq(InvestigationTranscript::getPid, param.getId())
                .eq(InvestigationTranscript::getTag, 1).eq(InvestigationTranscript::getDeleted, 0));
        if (count > 0) {
            return;
        }
        String ljlx = this.getTranscriptTypeById(param.getId());
        // String psnType = param.getPsnType();
        // String adultStatus = param.getAdultStatus();
        // 四种类型都自动生成：
        // 被调查人询问调查评估笔录、家属询问调查评估笔录、村社区干部询问调查评估笔录、村民询问调查评估笔录，这4个笔录
        // 1. 被调查人询问调查评估笔录
        PaperMaintenance paperMaintenance1 = paperMaintenanceService.getOne(new LambdaQueryWrapper<PaperMaintenance>().eq(PaperMaintenance::getPaperType, ljlx).eq(PaperMaintenance::getBlType, "BLLX06").eq(PaperMaintenance::getDelFlag, 0).eq(PaperMaintenance::getStatus, 1).orderByDesc(PaperMaintenance::getUpdateTime).last("limit 1"));
        if (paperMaintenance1 != null) {
            // 生成笔录
            this.addInvestigationTranscript(param, paperMaintenance1);
        }
        // 2. 家属询问调查评估笔录
        PaperMaintenance paperMaintenance2 = paperMaintenanceService.getOne(new LambdaQueryWrapper<PaperMaintenance>().eq(PaperMaintenance::getPaperType, ljlx).eq(PaperMaintenance::getBlType, "BLLX05").eq(PaperMaintenance::getDelFlag, 0).eq(PaperMaintenance::getStatus, 1).orderByDesc(PaperMaintenance::getUpdateTime).last("limit 1"));
        if (paperMaintenance2 != null) {
            // 生成笔录
            this.addInvestigationTranscript(param, paperMaintenance2);
        }
        // 3. 村社区干部询问调查评估笔录
        PaperMaintenance paperMaintenance3 = paperMaintenanceService.getOne(new LambdaQueryWrapper<PaperMaintenance>().eq(PaperMaintenance::getPaperType, ljlx).eq(PaperMaintenance::getBlType, "BLLX02").eq(PaperMaintenance::getDelFlag, 0).eq(PaperMaintenance::getStatus, 1).orderByDesc(PaperMaintenance::getUpdateTime).last("limit 1"));
        if (paperMaintenance3 != null) {
            // 生成笔录
            this.addInvestigationTranscript(param, paperMaintenance3);
        }
        // 4. 村民询问调查评估笔录
        PaperMaintenance paperMaintenance4 = paperMaintenanceService.getOne(new LambdaQueryWrapper<PaperMaintenance>().eq(PaperMaintenance::getPaperType, ljlx).eq(PaperMaintenance::getBlType, "BLLX03").eq(PaperMaintenance::getDelFlag, 0).eq(PaperMaintenance::getStatus, 1).orderByDesc(PaperMaintenance::getUpdateTime).last("limit 1"));
        if (paperMaintenance4 != null) {
            // 生成笔录
            this.addInvestigationTranscript(param, paperMaintenance4);
        }

        //更新主表笔录总数和已填写数量
        updateMainTableTranscriptCount(param.getId());
    }

    private void addInvestigationTranscript(InvestigationInfo param, PaperMaintenance paperMaintenance) {
        PaperMaintenanceParam paperMaintenanceParam = new PaperMaintenanceParam();
        paperMaintenanceParam.setId(paperMaintenance.getId());
        PaperMaintenance maintenance = paperMaintenanceService.detail(paperMaintenanceParam);

        InvestigationTranscript investigationTranscript = new InvestigationTranscript();
        investigationTranscript.setTitle(maintenance.getTitle());
        investigationTranscript.setPaperType(maintenance.getBlType());
        investigationTranscript.setPid(param.getId());
        investigationTranscript.setProgress("0");
        investigationTranscript.setTag(1);
        investigationTranscript.setDeleted(0);
        investigationTranscript.setContext(JSON.toJSONString(maintenance));
        InvestigationTranscriptParam investigationTranscriptParam = new InvestigationTranscriptParam();
        BeanUtils.copyProperties(investigationTranscript, investigationTranscriptParam);
        fillBasicInfo(investigationTranscriptParam);
        //如果已经存在该类型的笔录，那就不添加
        if (this.count(new LambdaQueryWrapper<InvestigationTranscript>().eq(InvestigationTranscript::getPid, param.getId()).eq(InvestigationTranscript::getPaperType, maintenance.getBlType()).eq(InvestigationTranscript::getDeleted, 0)) > 0) {
            return;
        }
        this.save(investigationTranscript);
    }


    /**
     * 填写并保存笔录
     *
     * @param transcriptParam 笔录信息
     * @return 保存后的笔录信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InvestigationTranscript fillAndSaveTranscript(InvestigationTranscriptParam transcriptParam) {
        if (transcriptParam == null) {
            throw new ServiceException(500, "笔录信息不能为空");
        }

//        // 检查必填字段
//        if (!StringUtils.hasText(transcriptParam.getPid())) {
//            throw new ServiceException(500, "调查评估主记录ID不能为空");
//        }
//
//        if (!StringUtils.hasText(transcriptParam.getTitle())) {
//            throw new ServiceException(500, "笔录标题不能为空");
//        }

        // 计算题目分数
        if (transcriptParam.getPaperMaintenanceParam() != null &&
                transcriptParam.getPaperMaintenanceParam().getTopicList() != null &&
                !transcriptParam.getPaperMaintenanceParam().getTopicList().isEmpty()) {

            // 计算总分
            int totalScore = 0;
            List<PaperTopicParam> topicList = transcriptParam.getPaperMaintenanceParam().getTopicList();

            // 遍历每个题目
            for (PaperTopicParam topic : topicList) {
                // 如果用户选择了答案
                if (StringUtils.hasText(topic.getUserSelectId()) && topic.getItemList() != null) {
                    // 查找用户选择的选项
                    for (PaperTopicItemParam item : topic.getItemList()) {
                        if (item.getId().equals(topic.getUserSelectId()) && item.getItemScore() != null) {
                            // 累加选项分数
                            totalScore += item.getItemScore();
                            break;
                        }
                    }
                }
            }

            // 设置总分
            transcriptParam.setScore(String.valueOf(totalScore));
            transcriptParam.getPaperMaintenanceParam().setScore(totalScore);

            // 将问卷JSON保存到笔录JSON中
            try {
                String paperJson = JSON.toJSONString(transcriptParam.getPaperMaintenanceParam());
                transcriptParam.setContext(paperJson);
            } catch (Exception e) {
                log.error("问卷JSON序列化失败", e);
                throw new ServiceException(500, "问卷JSON序列化失败：" + e.getMessage());
            }
        }
        //用于标记是否要生成笔录文书
        int tag = 0;
        InvestigationTranscript transcript = null;
        // 如果是新增笔录
        if (!StringUtils.hasText(transcriptParam.getId())) {
            // 设置删除标记为未删除
            transcriptParam.setDeleted(0);
            // 保存笔录
            transcript = new InvestigationTranscript();
            BeanUtils.copyProperties(transcriptParam, transcript);
            //处理基本信息
            if (transcriptParam.getInvestigationRecordParam() != null) {
                transcript.setBasicInfo(JSON.toJSONString(transcriptParam.getInvestigationRecordParam()));
            }
            transcript.setTag(1);
            this.save(transcript);
            if ("2".equals(transcript.getProgress())) {
                tag = 1;
            }
            log.info("新增笔录成功，ID：{}", transcriptParam.getId());
        } else {
            // 更新笔录
            // 先检查笔录是否存在
            InvestigationTranscript existTranscript = this.getById(transcriptParam.getId());
            if (existTranscript == null) {
                throw new ServiceException(500, "笔录不存在，ID：" + transcriptParam.getId());
            }
            if (!"2".equals(existTranscript.getProgress()) && "2".equals(transcriptParam.getProgress())) {
                //更新笔录时：数据库笔录状态不是已完成的才需要生成笔录，已完成的说明已经生成过了
                tag = 1;
            }
            //设置删除标记
            transcriptParam.setDeleted(0);
            // 更新笔录
            BeanUtils.copyProperties(transcriptParam, existTranscript);
            //处理基本信息
            if (transcriptParam.getInvestigationRecordParam() != null) {
                existTranscript.setBasicInfo(JSON.toJSONString(transcriptParam.getInvestigationRecordParam()));
            }
            boolean updated = this.updateById(existTranscript);
            if (!updated) {
                throw new ServiceException(500, "更新笔录失败，ID：" + transcriptParam.getId());
            }
            transcript = existTranscript;
            log.info("更新笔录成功，ID：{}", transcriptParam.getId());
        }

        //更新主表笔录总数和已填写数量
        updateMainTableTranscriptCount(transcriptParam.getPid());

        //如果是非暂存，判断是否完成所有笔录，如果完成了就计算一下得分
        if (transcriptParam.getProgress() != null && "2".equals(transcriptParam.getProgress())) {
            calculateScore(transcriptParam.getPid());
            try {
                //每次都拼接一下意见,报错了也能继续执行
                String opinion = createOpinionString(transcriptParam.getPid());
                //更新主表意见
                InvestigationGroup investigationGroup = investigationGroupMapper.selectById(transcriptParam.getPid());
                investigationGroup.setOpinion(opinion);
                investigationGroupMapper.updateById(investigationGroup);
            } catch (Exception e) {
                log.error("拼接意见失败", e);
            }
        }
        if (1 == tag) {
            //tag用于标记笔录是第一次完成，需要初始化笔录文书 并上传至Oss
            transcript.setTag(9);
        }
        // 返回保存后的笔录信息
        return transcript;
    }

    /**
     * 计算得分
     */
    private void calculateScore(String pid) {
//        Integer count = this.count(new LambdaQueryWrapper<InvestigationTranscript>()
//            .eq(InvestigationTranscript::getPid, pid)
//            .eq(InvestigationTranscript::getDeleted, 0)
//            .ne(InvestigationTranscript::getProgress, "2"));
//        // 如果没有未完成的笔录
//        if (count == 0) {
        //（每个笔录实际得分叠加）/（每个笔录指标总分）*100
        List<InvestigationTranscript> transcripts = this.list(new LambdaQueryWrapper<InvestigationTranscript>()
                .eq(InvestigationTranscript::getPid, pid)
                .eq(InvestigationTranscript::getDeleted, 0)
                .eq(InvestigationTranscript::getProgress, "2"));
        //计算每个笔录的总分和用户得分
        double totalScore = 0;
        double userScore = 0;
        for (InvestigationTranscript transcript : transcripts) {
            // 安全处理分数
            if (transcript.getScore() != null && !transcript.getScore().isEmpty()) {
                try {
                    userScore += Double.parseDouble(transcript.getScore());
                } catch (NumberFormatException e) {
                    log.error("笔录分数解析错误: {}", transcript.getScore(), e);
                    // 继续处理下一个笔录
                    continue;
                }
            }

            //解析问卷来计算问卷总分
            try {
                PaperMaintenanceParam paperMaintenanceParam = JSON.parseObject(transcript.getContext(), PaperMaintenanceParam.class);
                if (paperMaintenanceParam != null && paperMaintenanceParam.getTopicList() != null) {
                    for (PaperTopicParam paperTopicParam : paperMaintenanceParam.getTopicList()) {
                        if (paperTopicParam.getTopicScore() != null) {
                            totalScore += paperTopicParam.getTopicScore();
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析笔录内容失败: {}", transcript.getId(), e);
            }
        }

        // 防止除零
        double score = 0;
        if (totalScore > 0) {
            score = Math.min(100, Math.round((userScore / totalScore * 100) * 100.0) / 100.0); // 四舍五入到2位小数并限制最大值为100
        }

        //更新主表得分
        InvestigationInfo investigationInfo = investigationInfoMapper.selectById(pid);
        if (investigationInfo != null) {
            investigationInfo.setScore(score);
            investigationInfoMapper.updateById(investigationInfo);
        }
//        }
    }

    /**
     * 更新主表笔录总数和已填写数量
     */
    @Override
    public void updateMainTableTranscriptCount(String pid) {
        InvestigationInfo investigationInfo = investigationInfoMapper.selectById(pid);
        if (investigationInfo == null) {
            return;
        }
        // 更新主表笔录总数和已填写数量
        //计算总数
        LambdaQueryWrapper<InvestigationTranscript> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InvestigationTranscript::getPid, pid);
        queryWrapper.eq(InvestigationTranscript::getDeleted, 0);
        Integer totalCount = this.count(queryWrapper);
        investigationInfo.setRecordCount(totalCount);
        //计算已填写数量
        queryWrapper.eq(InvestigationTranscript::getProgress, 2);
        Integer filledCount = this.count(queryWrapper);
        investigationInfo.setRecordFillCount(filledCount);
        investigationInfoMapper.updateById(investigationInfo);
    }

    /**
     * 根据ID查询笔录详情
     *
     * @param id 笔录ID
     * @return 笔录详情，包含问卷信息
     */
    @Override
    public InvestigationTranscript getTranscriptById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new ServiceException(500, "笔录ID不能为空");
        }

        // 查询笔录信息
        InvestigationTranscript transcript = this.getById(id);
        if (transcript == null) {
            throw new ServiceException(500, "笔录不存在，ID：" + id);
        }

        // 解析笔录JSON内容为问卷对象
        if (StringUtils.hasText(transcript.getContext())) {
            try {
                PaperMaintenanceParam paperMaintenanceParam = JSON.parseObject(transcript.getContext(), PaperMaintenanceParam.class);
                transcript.setPaperMaintenanceParam(paperMaintenanceParam);
            } catch (JSONException e) {
                log.error("笔录JSON解析失败，ID：{}，错误：{}", id, e.getMessage(), e);
                throw new ServiceException(500, "笔录JSON解析失败：" + e.getMessage());
            }
        }

        //存储基本信息
        if (StringUtils.hasText(transcript.getBasicInfo())) {
            try {
                InvestigationRecordParam investigationRecordParam = JSON.parseObject(transcript.getBasicInfo(), InvestigationRecordParam.class);
                transcript.setInvestigationRecordParam(investigationRecordParam);
            } catch (JSONException e) {
                log.error("笔录基本信息JSON解析失败，ID：{}，错误：{}", id, e.getMessage(), e);
                throw new ServiceException(500, "笔录基本信息JSON解析失败：" + e.getMessage());
            }
        } else {
            //如果基本信息为空，则填充基本信息
            InvestigationRecordParam transcriptFillInfo = getTranscriptFillInfo(transcript);
            transcript.setInvestigationRecordParam(transcriptFillInfo);
        }
        //存储问卷信息
        if (StringUtils.hasText(transcript.getContext())) {
            try {
                PaperMaintenanceParam paperMaintenanceParam = JSON.parseObject(transcript.getContext(), PaperMaintenanceParam.class);
                transcript.setPaperMaintenanceParam(paperMaintenanceParam);
            } catch (JSONException e) {
                log.error("笔录问卷信息JSON解析失败，ID：{}，错误：{}", id, e.getMessage(), e);
                throw new ServiceException(500, "笔录问卷信息JSON解析失败：" + e.getMessage());
            }
        }

        return transcript;
    }

    @Override
    public void transcriptAdd(InvestigationTranscriptParam transcript) {

        // 根据量表id获取量表信息
        PaperMaintenanceParam paperMaintenanceParam = new PaperMaintenanceParam();
        paperMaintenanceParam.setId(transcript.getPaperId());
        PaperMaintenance paperMaintenance = paperMaintenanceService.detail(paperMaintenanceParam);
        if (paperMaintenance == null) {
            throw new ServiceException(500, "量表不存在，ID：" + transcript.getPaperId());
        }

        // 根据设置数量生成笔录
        for (int i = 0; i < transcript.getSetCount(); i++) {
            // 生成笔录
            InvestigationTranscript investigationTranscript = new InvestigationTranscript();
            BeanUtils.copyProperties(transcript, investigationTranscript);
            investigationTranscript.setTitle(paperMaintenance.getTitle());
            investigationTranscript.setPid(transcript.getPid());
            investigationTranscript.setProgress("0");
            investigationTranscript.setDeleted(0);
            investigationTranscript.setContext(JSON.toJSONString(paperMaintenance));
            investigationTranscript.setTag(1);
            this.save(investigationTranscript);
        }
        //更新主表笔录总数和已填写数量
        updateMainTableTranscriptCount(transcript.getPid());
        //页面添加清单后删除手动上传的记录
        /*this.lambdaUpdate()
                .set(InvestigationTranscript::getDeleted, 1)
                .eq(InvestigationTranscript::getPid, transcript.getPid())
                .eq(InvestigationTranscript::getTag, 2)
                .update();*/
    }

    @Override
    public void fillBasicInfo(InvestigationTranscriptParam transcript) {
        // 根据调查评估ID获取调查评估信息
        InvestigationInfo investigationInfo = investigationInfoMapper.selectById(transcript.getPid());
        if (investigationInfo == null) {
            throw new ServiceException(500, "调查评估不存在，ID：" + transcript.getPid());
        }
        //自动填充被调查人姓名、性别、出生年月
        InvestigationRecordParam investigationRecordParam = new InvestigationRecordParam();
        investigationRecordParam.setInvestigatedName(investigationInfo.getCorrectionObjName());
        investigationRecordParam.setInvestigatedGender(investigationInfo.getGender());
        investigationRecordParam.setInvestigatedBirthday(DateUtil.format(investigationInfo.getBirthday(), "yyyy-MM-dd"));

        //询问本人的时候，补充文化程度、身份证号码、工作单位、户籍地址、经常居住地、联系电话、案由
        if ("1".equals(transcript.getPaperType())) {
            investigationRecordParam.getPersonalInfo().setIdCard(investigationInfo.getCertNum());
            investigationRecordParam.getPersonalInfo().setWorkUnit(investigationInfo.getCompanyName());
            investigationRecordParam.getPersonalInfo().setRegisteredAddress(investigationInfo.getRegisteredAddress());
            investigationRecordParam.getPersonalInfo().setResidentialAddress(investigationInfo.getResidence());
            investigationRecordParam.getPersonalInfo().setContactPhone(investigationInfo.getContactTel());
            if (ObjectUtil.isNotEmpty(investigationInfo.getCriminalCharge())) {
                JSONArray criminalCharge = JSON.parseArray(investigationInfo.getCriminalCharge());
                Set<String> set = new HashSet<>();
                for (JSONObject object : criminalCharge.toJavaList(JSONObject.class)) {
                    set.add(object.getString("crimeDetail"));
                }
                investigationRecordParam.getPersonalInfo().setCaseReason(String.join(",", set));
            }
        }
    }

    @Override
    public String getTranscriptTypeById(String id) {
        // 根据调查评估ID获取调查评估信息
        InvestigationInfo investigationInfo = investigationInfoMapper.selectById(id);
        if (investigationInfo == null) {
            throw new ServiceException(500, "调查评估不存在，ID：" + id);
        }
        // 管制、缓刑 （成年） -》LJLX01
        // 假释（成年） -》LJLX02
        // 暂予监外执行（成年） -》LJLX03
        // 未成年 -》LJLX04
        //判断是否成年
        if ("0".equals(investigationInfo.getAdultStatus())) {
            return "LJLX04";
        }
        //拟适宜社区矫正类别
        String correctionType = investigationInfo.getCorrectionType();
        if ("1".equals(correctionType) || "2".equals(correctionType)) {
            return "LJLX01";
        } else if ("3".equals(correctionType)) {
            return "LJLX02";
        } else if ("4".equals(correctionType)) {
            return "LJLX03";
        }

        return null;
    }

    @Override
    public String initProcessInfo(JSONArray transcriptsArr, String correctionObjName, String psnType) {
        try {
            JSONArray arr = new JSONArray();
            JSONObject obj = null;
            for (int i = 0; i < transcriptsArr.size(); i++) {
                Object item = transcriptsArr.get(i);
                JSONObject transcriptItem;
                if (item instanceof JSONObject) {
                    transcriptItem = (JSONObject) item;
                } else {
                    // 如果是 Map 或其他类型，转换成 FastJSON 的 JSONObject
                    transcriptItem = JSON.parseObject(JSON.toJSONString(item));
                }
                JSONObject basicInfo = transcriptItem.getJSONObject("basicInfo");
                if (basicInfo == null) {
                    continue;
                }
                obj = new JSONObject();
                obj.put("dcsj", basicInfo.getString("startYear") + "-" + basicInfo.getString("startMonth") + "-" + basicInfo.getString("startDay"));
                obj.put("bdcrxm", basicInfo.getString("investigatedName"));
                obj.put("gx", basicInfo.getString("relationship"));
                obj.put("dd", basicInfo.getString("location"));
                String typeName = "";
                if ("1".equals(psnType)) {
                    typeName = "被告人";
                } else if ("2".equals(psnType)) {
                    typeName = "罪犯";
                } else if ("3".equals(psnType)) {
                    typeName = "犯罪嫌疑人";
                }
                obj.put("noticeTitle", typeName + correctionObjName + "实施社区矫正的调查评估");
                arr.add(obj);
            }
            return arr.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("initProcessInfo error:correctionObjName={}", correctionObjName);
        }

        return "[]";
    }

    @Override
    public InvestigationRecordParam getTranscriptFillInfo(InvestigationTranscript investigationTranscript) {
        InvestigationRecordParam investigationRecordParam = new InvestigationRecordParam();
        // 初始化所有子对象，避免空指针异常
        investigationRecordParam.setPersonalInfo(new InvestigationRecordParam.PersonalInfo());
        // investigationRecordParam.setVictimInfo(new InvestigationRecordParam.VictimInfo());
        // investigationRecordParam.setCommunityInfo(new InvestigationRecordParam.CommunityInfo());
        // investigationRecordParam.setGuarantorInfo(new InvestigationRecordParam.GuarantorInfo());
        // investigationRecordParam.setParoleGuarantorInfo(new InvestigationRecordParam.ParoleGuarantorInfo());

        InvestigationInfo investigationInfo = investigationInfoMapper.selectById(investigationTranscript.getPid());
        if (investigationInfo == null) {
            throw new ServiceException(500, "调查评估不存在，ID：" + investigationTranscript.getPid());
        }
        //调查时间回填，取当天

        investigationRecordParam.setStartYear(DateUtil.format(DateUtil.date(), "yyyy"));
        investigationRecordParam.setStartMonth(DateUtil.format(DateUtil.date(), "MM"));
        investigationRecordParam.setStartDay(DateUtil.format(DateUtil.date(), "dd"));
        investigationRecordParam.setStartHour(DateUtil.format(DateUtil.date(), "HH"));
        investigationRecordParam.setStartMinute(DateUtil.format(DateUtil.date(), "mm"));


        if (investigationInfo.getEndTime() != null) {
            investigationRecordParam.setEndYear(DateUtil.format(investigationInfo.getEndTime(), "yyyy"));
            investigationRecordParam.setEndMonth(DateUtil.format(investigationInfo.getEndTime(), "MM"));
            investigationRecordParam.setEndDay(DateUtil.format(investigationInfo.getEndTime(), "dd"));
            investigationRecordParam.setEndHour(DateUtil.format(investigationInfo.getEndTime(), "HH"));
            investigationRecordParam.setEndMinute(DateUtil.format(investigationInfo.getEndTime(), "mm"));
        }
        //被调查人信息回填
        investigationRecordParam.setInvestigatedName(investigationInfo.getCorrectionObjName());
        investigationRecordParam.setInvestigatedGender(investigationInfo.getGender());
        investigationRecordParam.setInvestigatedBirthday(DateUtil.format(investigationInfo.getBirthday(), "yyyy-MM-dd"));
        investigationRecordParam.setCorrectionPerson(investigationInfo.getCorrectionObjName());
        //本人信息回填

        investigationRecordParam.getPersonalInfo().setName(investigationInfo.getCorrectionObjName());
        investigationRecordParam.getPersonalInfo().setGender(investigationInfo.getGender());
        investigationRecordParam.getPersonalInfo().setBirthday(DateUtil.format(investigationInfo.getBirthday(), "yyyy-MM-dd"));
        investigationRecordParam.getPersonalInfo().setIdCard(investigationInfo.getCertNum());
        investigationRecordParam.getPersonalInfo().setWorkUnit(investigationInfo.getCompanyName());
        investigationRecordParam.getPersonalInfo().setRegisteredAddress(investigationInfo.getRegisteredAddress());
        investigationRecordParam.getPersonalInfo().setResidentialAddress(investigationInfo.getResidence());
        investigationRecordParam.getPersonalInfo().setContactPhone(investigationInfo.getContactTel());
        if (ObjectUtil.isNotEmpty(investigationInfo.getCriminalCharge())) {
            JSONArray criminalCharge = JSON.parseArray(investigationInfo.getCriminalCharge());
            Set<String> set = new HashSet<>();
            for (JSONObject object : criminalCharge.toJavaList(JSONObject.class)) {
                set.add(object.getString("crimeDetail"));
            }
            investigationRecordParam.getPersonalInfo().setCaseReason(String.join(",", set));
        }
        //曾用名
        investigationRecordParam.getPersonalInfo().setFormerName(investigationInfo.getUsedName());

        //文化程度
        investigationRecordParam.getPersonalInfo().setEducationLevel(investigationInfo.getWhcd());
        //籍贯用户籍地填充
        if (ObjectUtil.isNotEmpty(investigationInfo.getRegisteredAddressCode())) {
            try {
                String[] areaIds = investigationInfo.getRegisteredAddressCode().split(",");
                if (areaIds.length > 0) {
                    String areaName = areaInfoService.getFullChinese(areaIds[2]);
                    investigationRecordParam.getPersonalInfo().setNativePlace(areaName);
                }
            } catch (Exception e) {
                log.error("籍贯用户籍地填充失败，ID：{}", investigationInfo.getId());
            }
        }

        //如果有国籍或者民族，批量回填
        if (StringUtils.hasText(investigationInfo.getNationality())) {
            investigationRecordParam.getPersonalInfo().setNationality(investigationInfo.getNationality());
        }

        //调查人员回填
        InvestigationGroup investigationGroup = investigationGroupMapper.selectById(investigationInfo.getId());
        log.debug("调查人员回填，调查组ID：{}，调查组信息：{}", investigationGroup.getId(), investigationGroup);
        if (ObjectUtil.isNotEmpty(investigationGroup)) {
            JSONArray invePsnList = JSON.parseArray(investigationGroup.getInvePsnList());
            if (CollectionUtil.isNotEmpty(invePsnList)) {
                investigationRecordParam.setInvestigatorList(invePsnList);
                // 重构调查人员信息回填逻辑，支持多个调查人
                int size = invePsnList.size();
                for (int i = 0; i < size && i < 3; i++) { // 最多支持3名调查人
                    JSONObject jsonObject = invePsnList.getJSONObject(i);
                    String fullOrgName = jsonObject.getString("name");
                    String name = jsonObject.getString("nickName");

                    if (i == 0) { // 第一名调查人
                        if (StringUtils.hasText(fullOrgName)) {
                            String[] orgArray = fullOrgName.split("/");
                            if (orgArray.length >= 3) {
                                investigationRecordParam.setInvestigator1JudicialBureau(orgArray[1]);
                                investigationRecordParam.setInvestigator1JudicialOffice(orgArray[2].replace("司法所", ""));
                            }
                        }
                        if (StringUtils.hasText(name)) {
                            investigationRecordParam.setInvestigator1Name(name);
                        }
                    } else if (i == 1) { // 第二名调查人
                        if (StringUtils.hasText(fullOrgName)) {
                            String[] orgArray = fullOrgName.split("/");
                            if (orgArray.length >= 3) {
                                investigationRecordParam.setInvestigator2JudicialBureau(orgArray[1]);
                                investigationRecordParam.setInvestigator2JudicialOffice(orgArray[2].replace("司法所", ""));
                            }
                        }
                        if (StringUtils.hasText(name)) {
                            investigationRecordParam.setInvestigator2Name(name);
                        }
                    } else if (i == 2) { // 第三名调查人
                        if (StringUtils.hasText(fullOrgName)) {
                            String[] orgArray = fullOrgName.split("/");
                            if (orgArray.length >= 3) {
                                investigationRecordParam.setInvestigator3JudicialBureau(orgArray[1]);
                                investigationRecordParam.setInvestigator3JudicialOffice(orgArray[2].replace("司法所", ""));
                            }
                        }
                        if (StringUtils.hasText(name)) {
                            investigationRecordParam.setInvestigator3Name(name);
                        }
                    }
                }
            }
        }

        //记录人信息回填
        if (ObjectUtil.isNotEmpty(investigationInfo.getInveDeptName())) {
            String[] orgArray = investigationInfo.getInveDeptName().split("/");
            log.debug("orgArray: {},length: {}", orgArray, orgArray.length);
            if (orgArray.length >= 3) {  // 修改为判断长度>=3
                investigationRecordParam.setRecorderJudicialBureau(orgArray[1]);
                investigationRecordParam.setRecorderJudicialOffice(orgArray[2].replace("司法所", ""));
            }
        }

        //问题1、2回填
        if (ObjectUtil.isNotEmpty(investigationInfo.getInveDeptName())) {
            String[] orgArray = investigationInfo.getInveDeptName().split("/");
            if (orgArray.length >= 3) {  // 修改为判断长度>=3
                investigationRecordParam.setQuestion1Sfj(orgArray[1]);
                investigationRecordParam.setQuestion1Sfs(orgArray[2]);
            }
        }
        investigationRecordParam.setQuestion1Delegate(investigationInfo.getEntrustmentDeptName());
        investigationRecordParam.setQuestion1Name(investigationInfo.getCorrectionObjName());
        investigationRecordParam.setQuestion2Answer(null);


        // //每个子对象的被调查人姓名填充
        // investigationRecordParam.getVictimInfo().setCorrectionPerson(investigationInfo.getCorrectionObjName());
        // investigationRecordParam.getCommunityInfo().setCorrectionPerson(investigationInfo.getCorrectionObjName());
        // investigationRecordParam.getGuarantorInfo().setCorrectionPerson(investigationInfo.getCorrectionObjName());
        // investigationRecordParam.getParoleGuarantorInfo().setCorrectionPerson(investigationInfo.getCorrectionObjName());

        return investigationRecordParam;
    }

    @Override
    public String createOpinionString(String id) {
        //         XXX【姓名】，户籍地【户籍地址详情】：xx。家庭住址：xxxxxxxxx/无稳定居所。此次犯罪为过失犯罪，并且积极悔罪并愿意接受处罚，犯罪前无/有治安处罚记录，无/有犯罪记录，平时无/有(【酗酒】【赌博】【吸毒】)不良行为表现。

        // 家庭情况及态度：调查对象家庭关系融洽，家庭成员有父亲xxx，78岁，在家；母亲xxx，75岁，在家；妻子xxx，50岁，在德清县与他人合伙经营家政公司；女儿xxx，25岁，温州大学学生。其家庭成员具有监管能力且愿意监管，并支持配合社区矫正工作。

        // 村民委员会及邻居意见：村委会表示如调查对象实施社区矫正，愿意配合司法所落实社区矫正相关措施。调查对象入狱前与邻里不存在矛盾、冲突，邻居愿意接纳调查对象在本社区进行社区矫正。
        // 被害人意见：【是否取得被害人及其家属谅解】。

        // XXX的社区矫正调查评估总分为【分数】分，拟适宜/不适宜在所居住村实施社区矫正。

        try {
            log.info("开始生成调查意见字符串，ID：{}", id);
            InvestigationInfo investigationInfo = investigationInfoMapper.selectById(id);
            if (investigationInfo == null) {
                log.error("调查评估不存在，ID：{}", id);
                throw new ServiceException(500, "调查评估不存在，ID：" + id);
            }

            // 基本信息
            StringBuffer opinionString = new StringBuffer();
            opinionString.append(investigationInfo.getCorrectionObjName());
            //增加基本信息：性别、民族、出生日期、身份证号、婚姻状态
            if (StrUtil.isNotEmpty(investigationInfo.getGender())) {
                String xb = investigationGroupMapper.dictName("xb", investigationInfo.getGender());
                if (StrUtil.isNotEmpty(xb)) {
                    opinionString.append("，").append(xb);
                }
            }
            if (StrUtil.isNotEmpty(investigationInfo.getNationality())) {
                String mz = investigationGroupMapper.dictName("mz", investigationInfo.getNationality());
                if (StrUtil.isNotEmpty(mz)) {
                    opinionString.append("，").append(mz);
                }
            }
            if (ObjectUtil.isNotEmpty(investigationInfo.getBirthday())) {
                opinionString.append("，").append(DateUtil.format(investigationInfo.getBirthday(), DatePattern.CHINESE_DATE_PATTERN)).append("出生");
            }
            if (StrUtil.isNotEmpty(investigationInfo.getCertNum())) {
                opinionString.append("，身份证号：").append(investigationInfo.getCertNum());
            }
            if (StrUtil.isNotEmpty(investigationInfo.getMarriage())) {
                String hksz = investigationGroupMapper.dictName("DCPG_HYZK", investigationInfo.getMarriage());
                if (StrUtil.isNotEmpty(hksz)) {
                    opinionString.append("，").append(hksz);
                }
            }
            opinionString.append("，户籍地：").append(investigationInfo.getRegisteredAddress()).append("。家庭住址：");
            if (ObjectUtil.isNull(investigationInfo.getResidence())) {
                opinionString.append("无稳定居所。");
            } else {
                opinionString.append(investigationInfo.getResidence()).append("。");
            }

            //家庭成员信息
            String familyInfo = null;

            // 获取所有笔录信息
            LambdaQueryWrapper<InvestigationTranscript> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InvestigationTranscript::getPid, id);
            queryWrapper.eq(InvestigationTranscript::getDeleted, 0);
            queryWrapper.eq(InvestigationTranscript::getProgress, "2");
            List<InvestigationTranscript> investigationTranscriptList = this.list(queryWrapper);

            if (CollectionUtil.isEmpty(investigationTranscriptList)) {
                log.warn("未找到相关笔录信息，ID：{}", id);
                opinionString.append("未找到相关笔录信息，无法生成完整的调查意见。");
                return opinionString.toString();
            }

            // 被调查人问卷处理部分
            log.debug("处理被调查人问卷部分");
            boolean foundPersonalRecord = false;
            StringBuilder personalOpinion = new StringBuilder();
            for (InvestigationTranscript transcript : investigationTranscriptList) {
                if ("BLLX06".equals(transcript.getPaperType())) {
                    foundPersonalRecord = true;
                    try {
                        // 在这里处理找到的被调查人问卷
                        PaperMaintenanceParam paperMaintenanceParam = JSON.parseObject(transcript.getContext(), PaperMaintenanceParam.class);
                        if (paperMaintenanceParam != null && paperMaintenanceParam.getTopicList() != null) {
                            List<PaperTopicParam> topicList = paperMaintenanceParam.getTopicList();
                            // 房屋归属情况
                            String houseAnswer = getFirstAnswer(topicList, "房屋归属情况").trim();
                            if (StringUtils.hasText(houseAnswer)) {
                                personalOpinion.append("房屋情况：").append(houseAnswer).append("。");
                            }

                            // 主观恶性
                            String subjective = getFirstAnswer(topicList, "主观恶性").trim();
                            if (StringUtils.hasText(subjective)) {
                                personalOpinion.append("此次犯罪为").append(subjective).append("，");
                            }

                            // 是否认识到犯罪的危害性，积极悔罪
                            String remorse = getFirstAnswer(topicList, "是否认识到犯罪的危害性，积极悔罪").trim();
                            if (StringUtils.hasText(remorse)) {
                                personalOpinion.append(remorse).append("，");
                            }

                            // (涉嫌)犯罪前有无治安处罚记录
                            String securityRecord = getFirstAnswer(topicList, "(涉嫌)犯罪前有无治安处罚记录").trim();
                            if (StringUtils.hasText(securityRecord)) {
                                if (!securityRecord.contains("犯罪前")) {
                                    personalOpinion.append("犯罪前");
                                }
                                personalOpinion.append(securityRecord).append("，");
                            }

                            // (涉嫌)犯罪前有无犯罪记录
                            String criminalRecord = getFirstAnswer(topicList, "(涉嫌)犯罪前有无犯罪记录").trim();
                            if (StringUtils.hasText(criminalRecord)) {
                                personalOpinion.append(criminalRecord).append("，");
                            }

                            // 是否有吸毒史
                            String drugHistory = getFirstAnswer(topicList, "是否有吸毒史").trim();
                            // 是否有酗酒、赌博等不良嗜好或恶习
                            String badHabits = getFirstAnswer(topicList, "是否有酗酒、赌博等不良嗜好或恶习").trim();
                            //至少有上面一种
                            if (StringUtils.hasText(drugHistory) || StringUtils.hasText(badHabits)) {
                                if (StringUtils.hasText(drugHistory) && StringUtils.hasText(badHabits)) {
                                    // 两种情况都有，添加逗号分隔
                                    personalOpinion.append("平时").append(drugHistory).append("，").append(badHabits);
                                } else {
                                    // 只有一种情况
                                    personalOpinion.append("平时").append(drugHistory).append(badHabits);
                                }
                                personalOpinion.append("。");
                            } else {
                                personalOpinion.append("平时无不良行为表现。");
                            }

                            familyInfo = getFirstAnswerBackUserAnswer(topicList, "家庭成员").trim();
                        }
                    } catch (Exception e) {
                        log.error("处理被调查人问卷出错", e);
                        foundPersonalRecord = false;
                    }
                    break;
                }
            }

            if (foundPersonalRecord && personalOpinion.length() > 0) {
                opinionString.append(personalOpinion);
            } else {
                log.warn("未找到被调查人问卷或内容为空，ID：{}", id);
            }

            // 家庭情况及态度（家属询问调查评估笔录）
            log.debug("处理家庭情况部分");
            boolean foundFamilyRecord = false;
            StringBuilder familyOpinion = new StringBuilder();
            for (InvestigationTranscript transcript : investigationTranscriptList) {
                if ("BLLX05".equals(transcript.getPaperType())) {
                    foundFamilyRecord = true;
                    try {
                        // 在这里处理找到的家属问卷
                        PaperMaintenanceParam paperMaintenanceParam = JSON.parseObject(transcript.getContext(), PaperMaintenanceParam.class);
                        if (paperMaintenanceParam != null && paperMaintenanceParam.getTopicList() != null) {
                            List<PaperTopicParam> topicList = paperMaintenanceParam.getTopicList();

                            familyOpinion.append("\n家庭情况及态度：");

                            // 判断是否未成年人
                            boolean isMinor = "0".equals(investigationInfo.getAdultStatus());

                            // 根据是否未成年人，使用不同的表述
                            String familyMemberTerm = "家庭成员";

                            // 与家庭成员/监护人间是否关系融洽
                            String relationshipIndicator = isMinor ? "与监护人关系是否融洽" : "与家庭成员间是否关系融洽";
                            String familyRelation = getFirstAnswer(topicList, relationshipIndicator).trim();
                            // 如果没找到新的指标，尝试用原来的指标（兼容旧数据）
                            if (!StringUtils.hasText(familyRelation) && isMinor) {
                                familyRelation = getFirstAnswer(topicList, "与家庭成员间是否关系融洽").trim();
                            }

                            if (StringUtils.hasText(familyRelation)) {
                                familyOpinion.append("调查对象家庭关系").append(familyRelation).append("，");
                            }

                            // 家庭成员/监护人
                            // String membersIndicator = isMinor ? "监护人" : "家庭成员";
                            // String familyMembers = getFirstAnswer(topicList, membersIndicator).trim();
                            // // 如果没找到新的指标，尝试用原来的指标（兼容旧数据）
                            // if (!StringUtils.hasText(familyMembers) && isMinor) {
                            //     familyMembers = getFirstAnswer(topicList, "家庭成员").trim();
                            // }

                            if (StringUtils.hasText(familyInfo)) {
                                familyOpinion.append("家庭成员有").append(familyInfo);
                                if (!familyInfo.endsWith(".") && !familyInfo.endsWith("。")) {
                                    familyOpinion.append("。");
                                }
                            }

                            // 家庭成员/监护人是否具有监管能力且愿意监管
                            String supervisionIndicator = isMinor ? "监护人是否具有监管能力且愿意监管" : "家庭成员是否具有监管能力且愿意监管";
                            String supervision = getFirstAnswer(topicList, supervisionIndicator).trim();
                            // 如果没找到新的指标，尝试用原来的指标（兼容旧数据）
                            if (!StringUtils.hasText(supervision) && isMinor) {
                                supervision = getFirstAnswer(topicList, "家庭成员是否具有监管能力").trim();
                            }

                            if (StringUtils.hasText(supervision)) {
                                familyOpinion.append("其").append(familyMemberTerm).append(supervision).append("，");
                            }

                            // 家庭成员/监护人是否支持配合社区矫正工作
                            String cooperationIndicator = isMinor ? "监护人是否支持配合社区矫正工作" : "家庭成员是否支持配合社区矫正工作";
                            String cooperation = getFirstAnswer(topicList, cooperationIndicator).trim();
                            // 如果没找到新的指标，尝试用原来的指标（兼容旧数据）
                            if (!StringUtils.hasText(cooperation) && isMinor) {
                                cooperation = getFirstAnswer(topicList, "家庭成员是否支持配合社区矫正工作").trim();
                            }

                            if (StringUtils.hasText(cooperation)) {
                                if ("支持".equals(cooperation)) {
                                    familyOpinion.append(familyMemberTerm).append("支持配合社区矫正工作。");
                                } else if ("一般，不愿意表态".equals(cooperation)) {
                                    familyOpinion.append(familyMemberTerm).append("不愿意表态。");
                                } else if ("不支持".equals(cooperation)) {
                                    familyOpinion.append(familyMemberTerm).append("不支持配合社区矫正工作。");
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理家庭情况问卷出错", e);
                        foundFamilyRecord = false;
                    }
                    break;
                }
            }

            if (foundFamilyRecord && familyOpinion.length() > 0) {
                opinionString.append(familyOpinion);
            } else if (StringUtils.hasText(familyInfo)) {
                opinionString.append("\n家庭情况及态度：").append(familyInfo);
            } else {
                log.warn("未找到家庭情况问卷或内容为空，ID：{}", id);
            }

            //实际情况一般不调查村民，先注释掉
//            // 先处理村民意见，但暂不填充
//            String villagerAcceptance = null;
//            for (InvestigationTranscript transcript : investigationTranscriptList) {
//                if ("BLLX03".equals(transcript.getPaperType())) {
//                    try {
//                        PaperMaintenanceParam paperMaintenanceParam = JSON.parseObject(transcript.getContext(), PaperMaintenanceParam.class);
//                        if (paperMaintenanceParam != null && paperMaintenanceParam.getTopicList() != null) {
//                            List<PaperTopicParam> topicList = paperMaintenanceParam.getTopicList();
//
//                            // 获取村民意见但不立即填充到communityOpinion
//                            villagerAcceptance = getFirstAnswer(topicList, "邻居及社区居民是否愿意接纳").trim();
//                            log.debug("村民接纳意见处理完成，等待村委会处理后再填充");
//                        }
//                    } catch (Exception e) {
//                        log.error("处理村民意见问卷出错", e);
//                    }
//                    break;
//                }
//            }

            // 村民委员会及邻居意见
            log.debug("处理村委会及邻居意见部分");
            boolean foundCommunityRecord = false;
            StringBuilder communityOpinion = new StringBuilder();
            for (InvestigationTranscript transcript : investigationTranscriptList) {
                if ("BLLX02".equals(transcript.getPaperType())) {
                    foundCommunityRecord = true;
                    try {
                        PaperMaintenanceParam paperMaintenanceParam = JSON.parseObject(transcript.getContext(), PaperMaintenanceParam.class);
                        if (paperMaintenanceParam != null && paperMaintenanceParam.getTopicList() != null) {
                            List<PaperTopicParam> topicList = paperMaintenanceParam.getTopicList();

                            // 村委会意见，是否愿意配合监管
                            String villageOpinion = getFirstAnswer(topicList, "是否愿意配合监管").trim();
                            if (StringUtils.hasText(villageOpinion)) {
                                communityOpinion.append("\n村民委员会及邻居意见：");
                                if ("愿意配合监管".equals(villageOpinion)) {
                                    communityOpinion.append("村委会表示如调查对象实施社区矫正，愿意配合司法所落实社区矫正相关措施。");
                                } else {
                                    communityOpinion.append("村委会表示如调查对象实施社区矫正，不愿意配合司法所落实社区矫正相关措施。");
                                }
                            } else {
                                communityOpinion.append("\n村民委员会及邻居意见：村委会表示如调查对象实施社区矫正，愿意配合司法所落实社区矫正相关措施。");
                            }

                            // 邻里关系
                            String neighborhoodRelation = getFirstAnswer(topicList, "是否与邻居及社区居民等存在矛盾或冲突").trim();
                            if (StringUtils.hasText(neighborhoodRelation)) {
                                if ("不存在矛盾、冲突".equals(neighborhoodRelation)) {
                                    communityOpinion.append("调查对象入狱前与邻里不存在矛盾、冲突");
                                } else if ("存在较为轻微的矛盾或冲突".equals(neighborhoodRelation)) {
                                    communityOpinion.append("调查对象入狱前与邻里存在较为轻微的矛盾或冲突");
                                } else if ("存在严重的矛盾或冲突".equals(neighborhoodRelation)) {
                                    communityOpinion.append("调查对象入狱前与邻里存在严重的矛盾或冲突");
                                } else {
                                    communityOpinion.append("调查对象入狱前与邻里").append(neighborhoodRelation);
                                }
                                communityOpinion.append("，");
                            }

                            // 村委会处理完毕后，再填充村民意见
                            //2025年5月22日09:44:33 替换为村社区干部笔录中的是否同意接纳
                            String villagerAcceptance = getFirstAnswer(topicList, "是否同意接纳").trim();
                            if (StringUtils.hasText(villagerAcceptance)) {
                                if ("社区愿意接纳".equals(villagerAcceptance)) {
                                    communityOpinion.append("社区愿意接纳调查对象在本社区进行社区矫正。");
                                } else if ("社区不愿意接纳".equals(villagerAcceptance)) {
                                    communityOpinion.append("社区不愿意接纳调查对象在本社区进行社区矫正。");
                                } else {
                                    communityOpinion.append("社区对调查对象在本社区进行社区矫正表示无所谓。");
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理村委会及邻居意见问卷出错", e);
                        foundCommunityRecord = false;
                    }
                    break;
                }
            }

            if (foundCommunityRecord && communityOpinion.length() > 0) {
                opinionString.append(communityOpinion);
            } else {
                log.warn("未找到村委会及邻居意见问卷或内容为空，ID：{}", id);
            }

            // 是否取得被害人及其家属谅解
            // 没有被害人/取得被害人及其家属谅解/没有取得被害人及其家属谅解
            log.debug("处理被害人意见部分");
            String victimOpinion = "";
            for (InvestigationTranscript transcript : investigationTranscriptList) {
                if ("BLLX06".equals(transcript.getPaperType())) {
                    try {
                        PaperMaintenanceParam paperMaintenanceParam = JSON.parseObject(transcript.getContext(), PaperMaintenanceParam.class);
                        if (paperMaintenanceParam != null && paperMaintenanceParam.getTopicList() != null) {
                            List<PaperTopicParam> topicList = paperMaintenanceParam.getTopicList();
                            String opinion = getFirstAnswer(topicList, "是否取得被害人及其家属谅解").trim();
                            if (StringUtils.hasText(opinion)) {
                                if ("没有被害人".equals(opinion)) {
                                    victimOpinion = "没有被害人";
                                } else if ("取得被害人及其家属谅解".equals(opinion)) {
                                    victimOpinion = "取得被害人及其家属谅解";
                                } else if ("没有取得被害人及其家属谅解".equals(opinion)) {
                                    victimOpinion = "没有取得被害人及其家属谅解";
                                }
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理被害人意见问卷出错", e);
                    }
                }

            }

            if (StringUtils.hasText(victimOpinion)) {
                opinionString.append("\n被害人意见：").append(victimOpinion).append("。");
            } else {
                log.warn("未找到被害人意见信息，ID：{}", id);
            }

            // 社区矫正意见
            log.debug("生成社区矫正意见部分");
            opinionString.append("\n");
            double score = 0;
            try {
                score = investigationInfo.getScore() != null ? investigationInfo.getScore() : 0;
            } catch (Exception e) {
                log.error("获取调查评估分数出错", e);
            }

//            opinionString.append("综上所述，针对").append(investigationInfo.getCorrectionObjName())
//                    .append("在本辖区内的本次调查评估结论为");

            // 根据分数判断是否适宜
            if (score >= 80) {
                opinionString.append("适宜社区矫正。");
            } else {
                opinionString.append("不适宜社区矫正。");
            }

            return opinionString.toString();
        } catch (Exception e) {
            log.error("生成调查意见字符串出错，ID：{}", id, e);
            throw new ServiceException(500, "生成调查意见字符串失败：" + e.getMessage());
        }
    }

    /**
     * 根据指标来获取第一个题目的答案内容
     *
     * @param topicList 题目列表
     * @param indicator 指标名称
     * @return 答案内容，如果未找到则返回空字符串
     */
    private String getFirstAnswer(List<PaperTopicParam> topicList, String indicator) {
        if (topicList == null || topicList.isEmpty() || !StringUtils.hasText(indicator)) {
            return "";
        }

        return topicList.stream()
                .filter(item -> indicator.equals(item.getIndexName()))
                .findFirst()
                .map(item -> {
                    if (ObjectUtil.isNotEmpty(item.getItemList()) && StringUtils.hasText(item.getUserSelectId())) {
                        return item.getItemList().stream()
                                .filter(e -> item.getUserSelectId().equals(e.getId()))
                                .findFirst()
                                .map(i -> i.getContent() != null ? i.getContent() : "")
                                .orElse("");
                    }
                    return "";
                })
                .orElse("");
    }

    private String getFirstAnswerBackUserAnswer(List<PaperTopicParam> topicList, String indicator) {
        if (topicList == null || topicList.isEmpty() || !StringUtils.hasText(indicator)) {
            return "";
        }

        return topicList.stream()
                .filter(item -> indicator.equals(item.getIndexName()))
                .findFirst()
                .map(item -> {
                    if (StringUtils.hasText(item.getUserAnswer())) {
                        return item.getUserAnswer();
                    }
                    return "";
                })
                .orElse("");
    }

    @Override
    public String[] getJhr(JSONArray transcriptsArr) {
        try {
            for (int i = 0; i < transcriptsArr.size(); i++) {
                Object item = transcriptsArr.get(i);
                JSONObject transcriptItem;
                if (item instanceof JSONObject) {
                    transcriptItem = (JSONObject) item;
                } else {
                    // 如果是 Map 或其他类型，转换成 FastJSON 的 JSONObject
                    transcriptItem = JSON.parseObject(JSON.toJSONString(item));
                }
                if ("BLLX05".equals(transcriptItem.getString("paperType"))) {
                    //家属询问调查评估笔录 则 获取被调查人和关系
                    JSONObject basicInfo = transcriptItem.getJSONObject("basicInfo");
                    return new String[]{basicInfo.getString("investigatedName"), basicInfo.getString("relationship")};
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("getJhr error");
        }
        return null;
    }

    @Override
    @Transactional
    public void saveTranscriptHandMode(InvestigationTranscriptParam transcriptParam, String conclusion, String isDraft) {
        //删除之前的笔录
        if (ObjectUtil.isNotEmpty(transcriptParam.getBlFileIds())) {
            LambdaQueryWrapper<SysFileInfo> fileInfoWrapper = new LambdaQueryWrapper<>();
            fileInfoWrapper.eq(SysFileInfo::getBizId, transcriptParam.getPid());
            fileInfoWrapper.eq(SysFileInfo::getBizType, "eval_bl");
            fileInfoWrapper.notIn(SysFileInfo::getId, transcriptParam.getBlFileIds().split(","));
            sysFileInfoService.remove(fileInfoWrapper);
        }
        //手动上传的
        sysFileInfoService.setBiz(transcriptParam.getBlFileIds(), transcriptParam.getPid(), "eval_bl");

        // 保存笔录
        InvestigationTranscript transcript = new InvestigationTranscript();
        BeanUtils.copyProperties(transcriptParam, transcript);
        //设置为已完成
        transcript.setProgress("2");
        //默认设置为该类型，用于调查评估表初始化
        transcript.setPaperType("BLLX90");
        transcript.setTag(2);
        this.saveOrUpdate(transcript);
        log.info("新增手动笔录成功，ID：{}", transcriptParam.getId());
        //更新主表笔录总数和已填写数量
        updateMainTableTranscriptCount(transcriptParam.getPid());
        //非暂存才执行
        if (!"1".equals(isDraft)) {
            // 逻辑删除系统生成的笔录
            this.lambdaUpdate()
                    .set(InvestigationTranscript::getDeleted, 1)
                    .eq(InvestigationTranscript::getPid, transcriptParam.getPid())
                    .eq(InvestigationTranscript::getTag, 1)
                    .update();
            //初始化调查评估意见
            investigationGroupService.lambdaUpdate()
                    .set(InvestigationGroup::getOpinion, createOpinionStringHandleMode(transcriptParam.getPid(), transcriptParam, conclusion))
                    .eq(InvestigationGroup::getId, transcriptParam.getPid())
                    .update();
        }
    }

    /**
     * 拼调查评估意见
     *
     * @param id              调查评估的主表id
     * @param transcriptParam 笔录
     * @return
     */
    public String createOpinionStringHandleMode(String id, InvestigationTranscriptParam transcriptParam, String conclusion) {
        try {
            log.info("手动-开始生成调查意见字符串，ID：{}", id);
            InvestigationInfo investigationInfo = investigationInfoMapper.selectById(id);
            if (investigationInfo == null) {
                log.error("调查评估不存在，ID：{}", id);
                throw new ServiceException(500, "调查评估不存在，ID：" + id);
            }

            // 基本信息
            StringBuffer opinionString = new StringBuffer();
            opinionString.append(investigationInfo.getCorrectionObjName());
            //增加基本信息：性别、民族、出生日期、身份证号、婚姻状态
            if (StrUtil.isNotEmpty(investigationInfo.getGender())) {
                String xb = investigationGroupMapper.dictName("xb", investigationInfo.getGender());
                if (StrUtil.isNotEmpty(xb)) {
                    opinionString.append("，").append(xb);
                }
            }
            if (StrUtil.isNotEmpty(investigationInfo.getNationality())) {
                String mz = investigationGroupMapper.dictName("mz", investigationInfo.getNationality());
                if (StrUtil.isNotEmpty(mz)) {
                    opinionString.append("，").append(mz);
                }
            }
            if (ObjectUtil.isNotEmpty(investigationInfo.getBirthday())) {
                opinionString.append("，").append(DateUtil.format(investigationInfo.getBirthday(), DatePattern.CHINESE_DATE_PATTERN)).append("出生");
            }
            if (StrUtil.isNotEmpty(investigationInfo.getCertNum())) {
                opinionString.append("，身份证号：").append(investigationInfo.getCertNum());
            }
            if (StrUtil.isNotEmpty(investigationInfo.getMarriage())) {
                String hksz = investigationGroupMapper.dictName("DCPG_HYZK", investigationInfo.getMarriage());
                if (StrUtil.isNotEmpty(hksz)) {
                    opinionString.append("，").append(hksz);
                }
            }
            opinionString.append("，户籍地：").append(investigationInfo.getRegisteredAddress()).append("。家庭住址：");
            opinionString.append(investigationInfo.getCorrectionObjName()).append("，户籍地：")
                    .append(investigationInfo.getRegisteredAddress()).append("。家庭住址：");
            if (ObjectUtil.isNull(investigationInfo.getResidence())) {
                opinionString.append("无稳定居所。");
            } else {
                opinionString.append(investigationInfo.getResidence()).append("。");
            }

            JSONObject obj = JSONObject.parseObject(transcriptParam.getContext());
            //此次犯罪为
            if (ObjectUtil.isNotEmpty(obj.getString("zgex")) || ObjectUtil.isNotEmpty(obj.getString("rzhztd")) || ObjectUtil.isNotEmpty(obj.getString("zjsfqk"))
                    || ObjectUtil.isNotEmpty(obj.getString("sfyfzqk")) || ObjectUtil.isNotEmpty(obj.getString("ywblshxwex"))) {
                opinionString.append("\n此次犯罪为：");
                if (ObjectUtil.isNotEmpty(obj.getString("zgex"))) {
                    opinionString.append(obj.getString("zgex") + "，");
                }
                if (ObjectUtil.isNotEmpty(obj.getString("rzhztd"))) {
                    opinionString.append("并且" + obj.getString("rzhztd") + "，");
                }
                if (ObjectUtil.isNotEmpty(obj.getString("zjsfqk"))) {
                    opinionString.append(obj.getString("zjsfqk") + "，");
                }
                if (ObjectUtil.isNotEmpty(obj.getString("sfyfzqk"))) {
                    opinionString.append(obj.getString("sfyfzqk") + "，");
                }
                if (ObjectUtil.isNotEmpty(obj.getString("ywblshxwex"))) {
                    opinionString.append(obj.getString("ywblshxwex"));
                }
            }


            //家庭情况及态度
            if (ObjectUtil.isNotEmpty(obj.getString("jtcy")) || ObjectUtil.isNotEmpty(obj.getString("jtcytd"))) {
                opinionString.append("。\n家庭情况及态度：");
                if (ObjectUtil.isNotEmpty(obj.getString("jtcy"))) {
                    opinionString.append(obj.getString("jtcy"));
                }
                if (ObjectUtil.isNotEmpty(obj.getString("jtcytd"))) {
                    opinionString.append("，" + obj.getString("jtcytd"));
                }
                //opinionString.append("。\n家庭情况及态度：" + obj.getString("jtcy") + obj.getString("jtcytd"));
            }


            //村民委员会及邻居意见
            if (ObjectUtil.isNotEmpty(obj.getString("gzdwjdxxhcjczzjyj")) || ObjectUtil.isNotEmpty(obj.getString("shgztd"))) {
                opinionString.append("。\n村民委员会及邻居意见：");
                if (ObjectUtil.isNotEmpty(obj.getString("gzdwjdxxhcjczzjyj"))) {
                    opinionString.append(obj.getString("gzdwjdxxhcjczzjyj"));
                }
                if (ObjectUtil.isNotEmpty(obj.getString("shgztd"))) {
                    opinionString.append("，" + obj.getString("shgztd"));
                }
                // opinionString.append("。\n村民委员会及邻居意见：" + obj.getString("gzdwjdxxhcjczzjyj") + obj.getString("shgztd"));
            }

            //被害人意见
            if (ObjectUtil.isNotEmpty(obj.getString("bhrhqqstd"))) {
                opinionString.append("。\n被害人意见：" + obj.getString("bhrhqqstd"));
            }

            //综上所述
//            opinionString.append("。\n综上所述，针对").append(investigationInfo.getCorrectionObjName())
//                    .append("在本辖区内的本次调查评估结论为：");
            /*if (ObjectUtil.isNotEmpty(conclusion)) {
                opinionString.append("综上所述，针对").append(investigationInfo.getCorrectionObjName())
                        .append("在本辖区内的本次调查评估结论为");
                // 根据分数判断是否适宜
                if ("1".equals(conclusion)) {
                    opinionString.append("适宜社区矫正。");
                } else {
                    opinionString.append("不适宜社区矫正。");
                }
            }*/

            return opinionString.toString();
        } catch (Exception e) {
            log.error("手动-生成调查意见字符串出错，ID：{}", id, e);
            return "";
        }
    }
}
