package com.concise.gen.investigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.param.SysFileInfoVO;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.file.util.OssSignedUrlUtil;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.areainfo.entity.AreaInfo;
import com.concise.gen.areainfo.service.AreaInfoService;
import com.concise.gen.chargeinfo.service.ChargeInfoService;
import com.concise.gen.documentserial.service.DocumentSerialService;
import com.concise.gen.holidays.service.SysHolidayService;
import com.concise.gen.investigation.entity.*;
import com.concise.gen.investigation.mapper.InvestigationInfoMapper;
import com.concise.gen.investigation.param.*;
import com.concise.gen.investigation.service.*;
import com.concise.gen.investigationinfoflow.entity.InvestigationInfoFlow;
import com.concise.gen.investigationinfoflow.service.InvestigationInfoFlowService;
import com.concise.gen.investigationsign.service.InvestigationSignService;
import com.concise.gen.notice.enums.YwxtNoticeTypeEnum;
import com.concise.gen.notice.service.YwxtNoticeService;
import com.concise.gen.sysorg.service.OrgCommonService;
import com.concise.gen.webservice.service.SendInvestInfoService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 调查评估信息表service接口实现类
 *
 * <AUTHOR>
 * @date 2025-03-20 10:30:38
 */
@Service
@Slf4j
public class InvestigationInfoServiceImpl extends ServiceImpl<InvestigationInfoMapper, InvestigationInfo> implements InvestigationInfoService {

    @Resource
    private InvestigationGroupService investigationGroupService;
    @Resource
    private InvestigationTranscriptService investigationTranscriptService;
    @Resource
    private InvestigationDeliberationService investigationDeliberationService;
    @Resource
    private InvestigationReviewService investigationReviewService;
    @Resource
    private InvestigationApprovalService investigationApprovalService;
    @Resource
    private InvestigationFeedbackService investigationFeedbackService;
    @Resource
    private AreaInfoService areaInfoService;
    @Resource
    private OrgCommonService orgService;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private YwxtNoticeService ywxtNoticeService;
    @Resource
    private SendInvestInfoService sendService;
    @Resource
    private RuntimeService runtimeService;
    @Resource
    private InvestigationInfoFlowService investigationInfoFlowService;
    @Resource
    private TaskService taskService;
    @Resource
    private SysHolidayService sysHolidayService;
    @Resource
    private ChargeInfoService chargeInfoService;
    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;
    @Resource
    private DocumentSerialService documentSerialService;
    @Resource
    private InvestigationSignService investigationSignService;

    @Override
    public PageResult<InvestigationInfo> page(InvestigationInfoParam param) {
        QueryWrapper<InvestigationInfo> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(param.getStatus())) {
                queryWrapper.lambda().eq(InvestigationInfo::getStatus, param.getStatus());
            }
            // 根据委托类别(字典) 查询
            if (ObjectUtil.isNotEmpty(param.getEntrustmentType())) {
                queryWrapper.lambda().eq(InvestigationInfo::getEntrustmentType, param.getEntrustmentType());
            }
            // 根据委托单位名称 查询
            if (ObjectUtil.isNotEmpty(param.getEntrustmentDeptName())) {
                queryWrapper.lambda().like(InvestigationInfo::getEntrustmentDeptName, param.getEntrustmentDeptName());
            }
            // 调查结束时间开始
            if (ObjectUtil.isNotEmpty(param.getEntrustmentTimeStart())) {
                queryWrapper.lambda().ge(InvestigationInfo::getEndTime, param.getEntrustmentTimeStart());
            }
            // 调查结束时间结束
            if (ObjectUtil.isNotEmpty(param.getEntrustmentTimeEnd())) {
                queryWrapper.lambda().le(InvestigationInfo::getEndTime, param.getEntrustmentTimeEnd());
            }
            // 收到委托时间开始
            if (ObjectUtil.isNotEmpty(param.getInveTimeLimitStart())) {
                queryWrapper.lambda().ge(InvestigationInfo::getEntrustmentReceiveTime, param.getInveTimeLimitStart());
            }
            // 收到委托时间结束
            if (ObjectUtil.isNotEmpty(param.getInveTimeLimitEnd())) {
                queryWrapper.lambda().le(InvestigationInfo::getEntrustmentReceiveTime, param.getInveTimeLimitEnd());
            }
            // 根据接收单位 查询
            if (ObjectUtil.isNotEmpty(param.getReceiveDeptId())) {
                queryWrapper.lambda().and(i -> i.eq(InvestigationInfo::getReceiveDeptId, param.getReceiveDeptId()).or().like(InvestigationInfo::getReceiveDeptPids, param.getReceiveDeptId())
                        .or().like(InvestigationInfo::getInveDept, param.getReceiveDeptId()));
            }
            // 调查单位
            if (ObjectUtil.isNotEmpty(param.getInveDept())) {
                queryWrapper.apply("inve_dept in (select id from sys_org where pids like '%" + param.getInveDept() + "%' or id = '" + param.getInveDept() + "')");
            }
            // 根据调查对象姓名 查询
            if (ObjectUtil.isNotEmpty(param.getCorrectionObjName())) {
                queryWrapper.lambda().like(InvestigationInfo::getCorrectionObjName, param.getCorrectionObjName());
            }
            if (param.getNodeType() > 0) {
                switch (param.getNodeType()) {
                    case 1:
                        //待接收、待公告
                        queryWrapper.lambda().in(InvestigationInfo::getStatus, "PGZT01,PGZT02".split(","));
                        break;
                    case 2:
                        //待调查
                        queryWrapper.lambda().in(InvestigationInfo::getStatus, "PGZT03".split(","));
                        break;
                    case 3:
                        //待初审/小组意见、待初审/评议、待审批、待反馈
                        queryWrapper.lambda().in(InvestigationInfo::getStatus, "PGZT04,PGZT05,PGZT06,PGZT07".split(","));
                        break;
                    case 4:
                        //已完结、流程终止
                        queryWrapper.lambda().in(InvestigationInfo::getStatus, "PGZT08,PGZT98,PGZT99".split(","));
                        break;
                    default:
                }
            }
        }
        queryWrapper.lambda().eq(InvestigationInfo::getDeleted, 0);
        queryWrapper.lambda().orderByDesc(InvestigationInfo::getEntrustmentReceiveTime, InvestigationInfo::getCreateTime);
        Page<InvestigationInfo> rs = this.page(PageFactory.defaultPage(), queryWrapper);
        //转换百分比
        for (InvestigationInfo investigationInfo : rs.getRecords()) {
            investigationInfo.setPercent(changePercent(investigationInfo));
        }
        return new PageResult<>(rs);
    }

    private String changePercent(InvestigationInfo investigationInfo) {
        String percent = "0%";
        if (ObjectUtil.isEmpty(investigationInfo.getStatus())) {
            return percent;
        }
        switch (investigationInfo.getStatus()) {
            case "PGZT01":
                percent = "10%";
                break;
            case "PGZT02":
                percent = "15%";
                break;
            case "PGZT03":
                if (ObjectUtil.isEmpty(investigationInfo.getRecordCount()) || investigationInfo.getRecordCount() == 0) {
                    //待调查单位接收
                    percent = "20%";
                } else {
                    if (investigationInfo.getRecordCount().equals(investigationInfo.getRecordFillCount())) {
                        //查看和提交评估结果
                        percent = "60%";
                    } else {
                        //评估清单   根据清单完成情况计算百分比
                        double percentage = (investigationInfo.getRecordFillCount() / investigationInfo.getRecordCount()) * 100;
                        double rounded = Math.round(percentage * 10) / 10.0;
                        if (rounded <= 30) {
                            percent = "30%";
                        } else if (30 < rounded && rounded <= 60) {
                            percent = "40%";
                        } else if (60 < rounded && rounded <= 80) {
                            percent = "50%";
                        } else {
                            percent = "55%";
                        }
                    }
                }
                break;
            case "PGZT04":
                percent = "70%";
                break;
            case "PGZT05":
                percent = "75%";
                break;
            case "PGZT06":
                percent = "80%";
                break;
            case "PGZT07":
                percent = "90%";
                break;
            case "PGZT08":
                percent = "100%";
                break;
            default:
                percent = "0%";
        }
        return percent;
    }

    @Override
    @Transactional
    public String accept(InvestigationInfoParam param) {
        InvestigationInfo investigationInfo = this.getById(param.getId());
        if (null == investigationInfo) {
            //表示是页面新增的
            investigationInfo = new InvestigationInfo();
            BeanUtil.copyProperties(param, investigationInfo);
            investigationInfo.setEntrustmentType("2");
            if (ObjectUtil.isNotEmpty(investigationInfo.getInveDept())) {
                investigationInfo.setInveDeptName(orgService.getById(investigationInfo.getInveDept()).getName());
            }
            investigationInfo.setDayNum("1".equals(param.getExpeditedProcedure()) ? 5 : 10);
            //保存文书
            for (AcceptCorrectionDocParam doc : param.getDocList()) {
                SysFileInfo docFile = sysFileInfoService.getById(doc.getFileId());
                doc.setContactId(param.getId());
                doc.setId(IdUtil.fastSimpleUUID());
                doc.setOssUrl(docFile.getFilePath());
                docFile.setBizId(doc.getId());
                acceptCorrectionDocService.add(doc);
                sysFileInfoService.updateById(docFile);
            }
            if ("1".equals(param.getIsDraft())) {
                InvestigationGroup group = new InvestigationGroup();
                BeanUtil.copyProperties(investigationInfo, group);
                investigationGroupService.saveOrUpdate(group);
                //默认待接收
                investigationInfo.setStatus("PGZT01");
                this.save(investigationInfo);
                investigationInfoFlowService.draftTag(param.getId(), "PGZT01");
                return investigationInfo.getId();
            }
            // investigationInfo.setInveTimeLimit(sysHolidayService.countEndDate(new Date(), investigationInfo.getDayNum()));
            investigationInfo.setApprovalResult("1");
            this.save(investigationInfo);
            // 状态：待公告
            InvestigationGroup group = new InvestigationGroup();
            BeanUtil.copyProperties(investigationInfo, group);
            group.setInvePsnList(param.getInvePsnList().toJSONString());
            investigationGroupService.saveOrUpdate(group);
            // 状态：待公告 手动新增默认完成第一步 接收 直接到待公告
            runtimeService.startProcessInstanceByKey("DCPG", investigationInfo.getId());
            //更新流程表
            LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getWtTime, investigationInfo.getEntrustmentTime())
                    .set(InvestigationInfoFlow::getGwDeptId, investigationInfo.getEntrustmentDept())
                    .set(InvestigationInfoFlow::getGwDeptName, investigationInfo.getEntrustmentDeptName())
                    .set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                    .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                    .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                    .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                    .set(InvestigationInfoFlow::getSpTime, new Date())
                    .set(InvestigationInfoFlow::getType, 1)
                    .set(InvestigationInfoFlow::getFormVal, detail(param.getId(), null).toJSONString())
                    .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                    .eq(InvestigationInfoFlow::getStepCode, "PGZT01")
                    .eq(InvestigationInfoFlow::getType, 9);
            int num = investigationInfoFlowService.uptBackNum(flowLambdaUpdateWrapper);
            String activitiId = "";
            String processInstanceId = "";
            if (num > 0) {
                //公告文书号生成、公告正文由前端生成
                group.setNoticeDocNum(documentSerialService.getSerial(investigationInfo.getReceiveDeptId(), "WSLX01", param.getSName(), param.getId()));
                //若num = 0 则表示已接收过，是页面没跳到公告页后有点击了保存并到下一步，所以这边流程不用更新
                HashMap<String, Object> vars = new HashMap<>();
                vars.put("result", 1);
                Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
                if (null != task) {
                    activitiId = task.getId();
                    processInstanceId = task.getProcessInstanceId();
                    taskService.complete(task.getId(), vars);
                }
            }
            //更新基本信息状态为待公告
            this.lambdaUpdate()
                    .set(InvestigationInfo::getStatus, "PGZT02")
                    .set(InvestigationInfo::getActivitiId, activitiId)
                    .set(InvestigationInfo::getProcessInstanceId, processInstanceId)
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
            // group.setInvePsnList(param.getInvePsnList().toJSONString());
            investigationGroupService.saveOrUpdate(group);
            return investigationInfo.getId();
        }
        BeanUtil.copyProperties(param, investigationInfo);
        if (ObjectUtil.isNotEmpty(investigationInfo.getInveDept())) {
            investigationInfo.setInveDeptName(orgService.getById(investigationInfo.getInveDept()).getName());
        }
        investigationInfo.setDayNum("1".equals(param.getExpeditedProcedure()) ? 5 : 10);
        if (ObjectUtil.isEmpty(investigationInfo.getApprovalResult()) && investigationInfo.getId().length() < 25) {
            //说明是手动新增是暂存过的，默认设置通过
            investigationInfo.setApprovalResult("1");
        }
        this.updateById(investigationInfo);
        //更新文书
        Set<String> docIds = param.getDocList().stream().map(AcceptCorrectionDocParam::getId).collect(Collectors.toSet());
        if (docIds.size() > 0) {
            //删除的
            acceptCorrectionDocService.lambdaUpdate()
                    .eq(AcceptCorrectionDoc::getContactId, param.getId())
                    .notIn(AcceptCorrectionDoc::getId, docIds)
                    .remove();
        }
        for (AcceptCorrectionDocParam doc : param.getDocList()) {
            if (ObjectUtil.isEmpty(doc.getContactId())) {
                //新增的
                SysFileInfo docFile = sysFileInfoService.getById(doc.getFileId());
                doc.setContactId(param.getId());
                doc.setId(IdUtil.fastSimpleUUID());
                doc.setOssUrl(docFile.getFilePath());
                docFile.setBizId(doc.getId());
                docFile.setBizType("inve_receive");
                acceptCorrectionDocService.add(doc);
                sysFileInfoService.updateById(docFile);
            }
        }
        if ("1".equals(param.getIsDraft())) {
            InvestigationGroup group = new InvestigationGroup();
            BeanUtil.copyProperties(investigationInfo, group);
            investigationGroupService.saveOrUpdate(group);
            investigationInfoFlowService.draftTag(param.getId(), "PGZT01");
            return investigationInfo.getId();
        }

        if (ObjectUtil.isNotEmpty(param.getApprovalResult()) && !"1".equals(param.getApprovalResult())) {
            // 状态：流程结束
            param.setStepName("接收调查评估委托");
            backToOutSys(param);
            sysFileInfoService.setBizList(param.getReturnFiles(), investigationInfo.getId(), "LCZZ");
            sendService.sendMessage(investigationInfo,investigationGroupService.dictName("shjg",param.getApprovalResult()));
        } else {
            // 状态：待公告
            InvestigationGroup group = new InvestigationGroup();
            BeanUtil.copyProperties(investigationInfo, group);
            group.setInvePsnList(param.getInvePsnList().toJSONString());
            investigationGroupService.saveOrUpdate(group);
            //更新流程表
            LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getWtTime, investigationInfo.getEntrustmentTime())
                    .set(InvestigationInfoFlow::getGwDeptId, investigationInfo.getEntrustmentDept())
                    .set(InvestigationInfoFlow::getGwDeptName, investigationInfo.getEntrustmentDeptName())
                    .set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                    .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                    .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                    .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                    .set(InvestigationInfoFlow::getSpTime, new Date())
                    .set(InvestigationInfoFlow::getType, 1)
                    .set(InvestigationInfoFlow::getFormVal, detail(param.getId(), null).toJSONString())
                    .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                    .eq(InvestigationInfoFlow::getStepCode, "PGZT01")
                    .eq(InvestigationInfoFlow::getType, 9);
            int num = investigationInfoFlowService.uptBackNum(flowLambdaUpdateWrapper);
            String activitiId = "";
            String processInstanceId = "";
            if (num > 0) {
                //公告文书号生成、公告正文由前端生成
                group.setNoticeDocNum(documentSerialService.getSerial(investigationInfo.getReceiveDeptId(), "WSLX01", param.getSName(), param.getId()));
                //若num = 0 则表示已接收过，是页面没跳到公告页后有点击了保存并到下一步，所以这边流程不用更新
                HashMap<String, Object> vars = new HashMap<>();
                vars.put("result", 1);
                Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
                if (null != task) {
                    activitiId = task.getId();
                    processInstanceId = task.getProcessInstanceId();
                    taskService.complete(task.getId(), vars);
                }
            }
            //更新基本信息状态为待公告
            this.lambdaUpdate()
                    .set(InvestigationInfo::getStatus, "PGZT02")
                    .set(InvestigationInfo::getActivitiId, activitiId)
                    .set(InvestigationInfo::getProcessInstanceId, processInstanceId)
                    // .set(InvestigationInfo::getInveTimeLimit, sysHolidayService.countEndDate(new Date(), investigationInfo.getDayNum()))
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
            // group.setInvePsnList(param.getInvePsnList().toJSONString());
            investigationGroupService.saveOrUpdate(group);
            sendService.sendMessage(investigationInfo,null);
        }
        return investigationInfo.getId();
    }

    @Override
    @Transactional
    public void publishNotice(InvestigationGroupParam param) {
        if (ObjectUtil.isNotEmpty(param.getNoticeDocNumTwo())) {
            param.setNoticeDocNum(param.getNoticeDocNumOne() + (Integer.parseInt(param.getNoticeDocNumTwo()) > 99 ? param.getNoticeDocNumTwo() : String.format("%03d", Integer.parseInt(param.getNoticeDocNumTwo())))
                    + param.getNoticeDocNumThree());
        }
        InvestigationGroup group = new InvestigationGroup();
        BeanUtil.copyProperties(param, group);
        investigationGroupService.updateById(group);
        //删除之前的文书
        LambdaQueryWrapper<SysFileInfo> fileInfoWrapper = new LambdaQueryWrapper<>();
        fileInfoWrapper.eq(SysFileInfo::getBizId, param.getId());
        fileInfoWrapper.eq(SysFileInfo::getBizType, "inve_notice");
        fileInfoWrapper.notIn(SysFileInfo::getId, param.getNoticeDocFiles().split(","));
        sysFileInfoService.remove(fileInfoWrapper);
        //保存
        sysFileInfoService.setBiz(param.getNoticeDocFiles(), param.getId(), "inve_notice");
        if ("1".equals(param.getIsDraft())) {
            investigationInfoFlowService.draftTag(param.getId(), "PGZT02");
            //更新基本信息
            String psns = "";
            if (ObjectUtil.isNotEmpty(group.getInvePsnList())) {
                JSONArray psnArr = JSONArray.parseArray(group.getInvePsnList());
                for (int i = 0; i < psnArr.size(); i++) {
                    JSONObject obj = (JSONObject) psnArr.get(i);
                    psns += obj.getString("nickName") + ",";
                }
            }
            this.lambdaUpdate()
                    .set(InvestigationInfo::getInveDept, group.getInveDept())
                    .set(InvestigationInfo::getInveDeptName, ObjectUtil.isNotEmpty(group.getInveDept()) ? orgService.getById(group.getInveDept()).getName() : "")
                    .set(InvestigationInfo::getCorrectionObjName, group.getCorrectionObjName())
                    .set(InvestigationInfo::getNoticeDocNum, group.getNoticeDocNum())
                    .set(InvestigationInfo::getPsns, ObjectUtil.isNotEmpty(psns) ? psns.substring(0, psns.length() - 1) : "")
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
            return;
        }
        // 发送浙政钉工作通知
        investigationGroupService.sendZwddMsg(group);
        investigationTranscriptService.genEmptyTranscript(this.getById(param.getId()));
        // 状态：待调查
        HashMap<String, Object> vars = new HashMap<>();
        vars.put("result", 1);
        Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
        if (null != task) {
            taskService.complete(task.getId(), vars);
        }
        //更新流程表
        InvestigationInfo investigation = this.getById(param.getId());
        LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getWtTime, param.getNoticeTime())
                .set(InvestigationInfoFlow::getGwDeptId, investigation.getReceiveDeptId())
                .set(InvestigationInfoFlow::getGwDeptName, investigation.getReceiveDeptName())
                .set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                .set(InvestigationInfoFlow::getSpTime, new Date())
                .set(InvestigationInfoFlow::getType, 1)
                .set(InvestigationInfoFlow::getFormVal, detail(param.getId(), null).toJSONString())
                .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                .eq(InvestigationInfoFlow::getStepCode, "PGZT02")
                .eq(InvestigationInfoFlow::getType, 9);
        investigationInfoFlowService.update(flowLambdaUpdateWrapper);
        //更新基本信息状态为待调查
        String psns = "";
        if (ObjectUtil.isNotEmpty(group.getInvePsnList())) {
            JSONArray psnArr = JSONArray.parseArray(group.getInvePsnList());
            for (int i = 0; i < psnArr.size(); i++) {
                JSONObject obj = (JSONObject) psnArr.get(i);
                psns += obj.getString("nickName") + ",";
            }
        }
        this.lambdaUpdate()
                .set(InvestigationInfo::getStatus, "PGZT03")
                .set(InvestigationInfo::getTag, 0)
                .set(InvestigationInfo::getInveDept, group.getInveDept())
                .set(InvestigationInfo::getInveDeptName, ObjectUtil.isNotEmpty(group.getInveDept()) ? orgService.getById(group.getInveDept()).getName() : "")
                .set(InvestigationInfo::getCorrectionObjName, group.getCorrectionObjName())
                .set(InvestigationInfo::getNoticeDocNum, group.getNoticeDocNum())
                .set(InvestigationInfo::getPsns, ObjectUtil.isNotEmpty(psns) ? psns.substring(0, psns.length() - 1) : "")
                .eq(InvestigationInfo::getId, param.getId())
                .update();
    }

    //调查评估_调查接收 （第一步）
    @Override
    @Transactional
    public void groupInvestigateAccept(InvestigationGroupParam param) {

        InvestigationGroup data = new InvestigationGroup();
        BeanUtil.copyProperties(param, data);
        investigationGroupService.saveOrUpdate(data);
        if ("1".equals(param.getIsDraft())) {
            if (!"1".equals(param.getApprovalResult())) {
                // 未接受情况
                sysFileInfoService.setBizList(param.getReturnFiles(), param.getId(), "inve_return2");
                investigationInfoFlowService.draftTag(param.getId(), "PGZT03_1");
            }
            return;
        }
        if (!"1".equals(param.getApprovalResult())) {
            // 退回 状态：待公告
            HashMap<String, Object> vars = new HashMap<>();
            vars.put("result", 2);
            Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
            if (null != task) {
                taskService.complete(task.getId(), vars);
            }
            //更新流程记录
            investigationInfoFlowService.ddcBack(param);
            //更新基本信息状态为待公告
            this.lambdaUpdate()
                    .set(InvestigationInfo::getStatus, "PGZT02")
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();

            // 未接受情况
            sysFileInfoService.setBizList(param.getReturnFiles(), param.getId(), "inve_return2");
        } else {
            //更新流程记录
            LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                    .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                    .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                    .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                    .set(InvestigationInfoFlow::getSpTime, new Date())
                    .set(InvestigationInfoFlow::getType, 1)
                    .set(InvestigationInfoFlow::getFormVal, detail(param.getId(), null).toJSONString())
                    .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                    .eq(InvestigationInfoFlow::getStepCode, "PGZT03_1")
                    .eq(InvestigationInfoFlow::getType, 9);
            investigationInfoFlowService.update(flowLambdaUpdateWrapper);
            //更新tag
            this.lambdaUpdate()
                    .set(InvestigationInfo::getTag, 1)
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
            // todo 填写笔录
        }
    }

    /**
     * 调查评估_管理评估清单点击下一步 （第二步）
     */
    @Override
    @Transactional
    public void groupInvestigateList(InvestigationGroupParam param) {
        // 点击下一步时只需更新对应的流程记录
        //保存附件
        if (ObjectUtil.isNotEmpty(param.getOtherFiles())) {
            LambdaQueryWrapper<SysFileInfo> fileInfoWrapper = new LambdaQueryWrapper<>();
            fileInfoWrapper.eq(SysFileInfo::getBizId, param.getId());
            fileInfoWrapper.eq(SysFileInfo::getBizType, "inve_pgqd");
            fileInfoWrapper.notIn(SysFileInfo::getId, param.getOtherFiles().split(","));
            sysFileInfoService.remove(fileInfoWrapper);
        }
        sysFileInfoService.setBiz(param.getOtherFiles(), param.getId(), "inve_pgqd");
        //保存基层组织意见附件和辖区公安派出所意见附件
        if (ObjectUtil.isNotEmpty(param.getJcyjFile()) || ObjectUtil.isNotEmpty(param.getGayjFile())) {
            StringJoiner newIdsJoiner = new StringJoiner(",");
            Optional.ofNullable(param.getJcyjFile()).ifPresent(newIdsJoiner::add);
            Optional.ofNullable(param.getGayjFile()).ifPresent(newIdsJoiner::add);
            String newIds = newIdsJoiner.toString();
            sysFileInfoService.lambdaUpdate()
                    .set(SysFileInfo::getDelFlag, 1)
                    .eq(SysFileInfo::getBizId, param.getId())
                    .in(SysFileInfo::getBizType, "JCZZYJ", "PCSYJ")
                    .notIn(SysFileInfo::getId, newIds.split(","))
                    .update();
        }
        sysFileInfoService.setBiz(param.getJcyjFile(), param.getId(), "JCZZYJ");
        sysFileInfoService.setBiz(param.getGayjFile(), param.getId(), "PCSYJ");
        if (null != param.getTag() && 2 == param.getTag()) {
            //如果笔录是手动上传的需要更新 基础表的分数为0
            this.lambdaUpdate()
                    .set(InvestigationInfo::getTag, 2)
                    .set(InvestigationInfo::getScore, null)
                    .set(InvestigationInfo::getTranscriptTag, 2)
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
        } else {
            //更新tag
            this.lambdaUpdate()
                    .set(InvestigationInfo::getTag, 2)
                    .set(InvestigationInfo::getTranscriptTag, 1)
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
            // 逻辑删除手动生成的笔录
            investigationTranscriptService.lambdaUpdate()
                    .set(InvestigationTranscript::getDeleted, 1)
                    .eq(InvestigationTranscript::getPid, param.getId())
                    .eq(InvestigationTranscript::getTag, 2)
                    .update();
        }

        LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                .set(InvestigationInfoFlow::getSpTime, new Date())
                .set(InvestigationInfoFlow::getType, 1)
                .set(InvestigationInfoFlow::getFormVal, detail(param.getId(), null).toJSONString())
                .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                .eq(InvestigationInfoFlow::getStepCode, "PGZT03_2")
                .eq(InvestigationInfoFlow::getType, 9);
        investigationInfoFlowService.update(flowLambdaUpdateWrapper);
    }

    /**
     * 调查评估_提交 （第三步）
     */
    @Override
    @Transactional
    public void groupInvestigateCommit(InvestigationGroupParam param) {
        InvestigationGroup data = new InvestigationGroup();
        BeanUtil.copyProperties(param, data);
        investigationGroupService.saveOrUpdate(data);
        sysFileInfoService.removeBizBind(param.getId(), "eval_draft");
        sysFileInfoService.removeBizBind(param.getId(), "inve_dctj");
        sysFileInfoService.setBiz(param.getEvaluationFiles(), param.getId(), "eval_draft");
        sysFileInfoService.setBiz(param.getOtherFiles(), param.getId(), "inve_dctj");
        //判断手动上传的笔录附件是否有值，有则更新
        if (ObjectUtil.isNotEmpty(param.getBlFileIds())) {
            //删除之前的笔录
            LambdaQueryWrapper<SysFileInfo> fileInfoWrapper = new LambdaQueryWrapper<>();
            fileInfoWrapper.eq(SysFileInfo::getBizId, param.getId());
            fileInfoWrapper.eq(SysFileInfo::getBizType, "eval_bl");
            fileInfoWrapper.notIn(SysFileInfo::getId, param.getBlFileIds().split(","));
            sysFileInfoService.remove(fileInfoWrapper);
            //手动上传的
            sysFileInfoService.setBiz(param.getBlFileIds(), param.getId(), "eval_bl");
        }
        if ("1".equals(param.getIsDraft())) {
            investigationInfoFlowService.draftTag(param.getId(), "PGZT03_3");
            return;
        }
        //初始化小组合议
        InvestigationDeliberation investigationDeliberation = new InvestigationDeliberation();
        investigationDeliberation.setId(param.getId());
        investigationDeliberation.setInveDept(param.getOtherInfo().getString("inveDept"));
        investigationDeliberation.setCorrectionObjName(param.getOtherInfo().getString("correctionObjName"));
        investigationDeliberation.setDocNum(param.getNoticeDocNum());
        investigationDeliberation.setConclusion(param.getConclusion());
        investigationDeliberation.setParticular(param.getOpinion());
        investigationDeliberationService.saveOrUpdate(investigationDeliberation);
        // 通过 下一个状态 待初审/小组意见
        HashMap<String, Object> vars = new HashMap<>();
        vars.put("result", 1);
        Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
        if (null != task) {
            taskService.complete(task.getId(), vars);
        }
        //更新流程记录
        LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                .set(InvestigationInfoFlow::getMsg, "1".equals(param.getConclusion()) ? "适宜社区矫正" : "不适宜社区矫正")
                .set(InvestigationInfoFlow::getSpTime, new Date())
                .set(InvestigationInfoFlow::getType, 1)
                .set(InvestigationInfoFlow::getFormVal, detail(param.getId(), null).toJSONString())
                .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                .eq(InvestigationInfoFlow::getStepCode, "PGZT03_3")
                .eq(InvestigationInfoFlow::getType, 9);
        investigationInfoFlowService.update(flowLambdaUpdateWrapper);
        //更新基本信息状态为待初审/小组意见
        this.lambdaUpdate()
                .set(InvestigationInfo::getStatus, "PGZT04")
                .eq(InvestigationInfo::getId, param.getId())
                .update();
    }

    @Override
    @Transactional
    public void deliberation(InvestigationDeliberationParam param) {
        InvestigationDeliberation data = new InvestigationDeliberation();
        BeanUtil.copyProperties(param, data, "evaluationFiles");
        investigationDeliberationService.saveOrUpdate(data);
        investigationDeliberationService.updateHistoryField("PGZT04", this.getById(param.getId()).getInveDept(), param.getHost(), param.getAddress(), param.getPsnList(), param.getRecorder());
        if (ObjectUtil.isNotEmpty(param.getOpinionFiles())) {
            //删除之前的文书
            LambdaQueryWrapper<SysFileInfo> fileInfoWrapper = new LambdaQueryWrapper<>();
            fileInfoWrapper.eq(SysFileInfo::getBizId, param.getId());
            fileInfoWrapper.eq(SysFileInfo::getBizType, "opinion_deliberate");
            fileInfoWrapper.notIn(SysFileInfo::getId, param.getOpinionFiles().split(","));
            sysFileInfoService.remove(fileInfoWrapper);
            //附件： 小组合议意见表
            //判断是否有最新已签名的,非手动上传的
            if (!judgeFileType(param.getOpinionFiles())) {
                String fileIds = investigationSignService.retroSignature(data.getId(), param.getOpinionFiles(), "opinion_deliberate");
                sysFileInfoService.setBiz(fileIds, param.getId(), "opinion_deliberate");
            } else {
                //手动上传的
                sysFileInfoService.setBiz(param.getOpinionFiles(), param.getId(), "opinion_deliberate");
            }
        }
        sysFileInfoService.removeBizBind(param.getId(), "eval_deliberate");
        //判断是否有最新已签名的
        if (ObjectUtil.isNotEmpty(param.getEvaluationFiles())) {
            if (!judgeFileType(param.getEvaluationFiles())) {
                String fileIds = investigationSignService.retroSignature(data.getId(), param.getEvaluationFiles(), "eval_deliberate");
                sysFileInfoService.setBiz(fileIds, param.getId(), "eval_deliberate");
            } else {
                //手动上传的
                sysFileInfoService.setBiz(param.getEvaluationFiles(), param.getId(), "eval_deliberate");
            }
        }
        if ("1".equals(param.getIsDraft())) {
            investigationInfoFlowService.draftTag(param.getId(), "PGZT04");
            return;
        }
        if ("1".equals(param.getApprovalResult())) {
            //初始化集体评议
            InvestigationReview investigationReview = new InvestigationReview();
            investigationReview.setId(param.getId());
            investigationReview.setDocNum(data.getDocNum());
            investigationReview.setConclusion(data.getConclusion());
            investigationReview.setParticular(data.getParticular());
            investigationReviewService.saveOrUpdate(investigationReview);
            // 通过 下一个状态待初审/集体评议
            HashMap<String, Object> vars = new HashMap<>();
            vars.put("result", 1);
            Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
            if (null != task) {
                taskService.complete(task.getId(), vars);
            }
            //更新流程记录
            LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                    .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                    .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                    .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                    .set(InvestigationInfoFlow::getMsg, param.getApprovalRemark())
                    .set(InvestigationInfoFlow::getSpTime, new Date())
                    .set(InvestigationInfoFlow::getType, 1)
                    .set(InvestigationInfoFlow::getFormVal, detail(param.getId(), null).toJSONString())
                    .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                    .eq(InvestigationInfoFlow::getStepCode, "PGZT04")
                    .eq(InvestigationInfoFlow::getType, 9);
            investigationInfoFlowService.update(flowLambdaUpdateWrapper);
            //更新基本信息状态为待初审/集体评议
            this.lambdaUpdate()
                    .set(InvestigationInfo::getStatus, "PGZT05")
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
        } else {
            //退回 状态：待调查第一步
            HashMap<String, Object> vars = new HashMap<>();
            vars.put("result", 2);
            Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
            if (null != task) {
                taskService.complete(task.getId(), vars);
            }
            //更新流程记录
            investigationInfoFlowService.xzyjBack(param);
            //更新基本信息状态为待调查
            this.lambdaUpdate()
                    .set(InvestigationInfo::getStatus, "PGZT03")
                    .set(InvestigationInfo::getConclusion, null)
                    .set(InvestigationInfo::getTag, 0)
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
            //groups里的结论也要置空
            investigationGroupService.lambdaUpdate()
                    .set(InvestigationGroup::getConclusion, null)
                    .eq(InvestigationGroup::getId, param.getId())
                    .update();
        }

    }

    /**
     * 判断文件是手动上传还是制作的文书
     *
     * @param fileIds
     */
    private boolean judgeFileType(String fileIds) {
        try {
            // 获取文书列表
            List<SysFileInfo> fileList = sysFileInfoService.lambdaQuery()
                    .in(SysFileInfo::getId, Arrays.asList(fileIds.split(",")))
                    .list();
            if (fileList.isEmpty()) {
                return false;
            }
            for (SysFileInfo file : fileList) {
                if (ObjectUtil.isNotEmpty(file.getType())) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("判断文件类型失败", e);
            return false;
        }
    }


    @Override
    @Transactional
    public void review(InvestigationReviewParam param) {
        InvestigationReview data = new InvestigationReview();
        BeanUtil.copyProperties(param, data, "evaluationFiles");
        investigationReviewService.saveOrUpdate(data);
        investigationDeliberationService.updateHistoryField("PGZT05", this.getById(param.getId()).getInveDept(), param.getHost(), param.getAddress(), param.getPsnList(), param.getRecorder());
        if (ObjectUtil.isNotEmpty(param.getOpinionFiles())) {
            //删除之前的文书
            LambdaQueryWrapper<SysFileInfo> fileInfoWrapper = new LambdaQueryWrapper<>();
            fileInfoWrapper.eq(SysFileInfo::getBizId, param.getId());
            fileInfoWrapper.eq(SysFileInfo::getBizType, "opinion_review");
            fileInfoWrapper.notIn(SysFileInfo::getId, param.getOpinionFiles().split(","));
            sysFileInfoService.remove(fileInfoWrapper);
            //附件： 集体评议意见表
            //判断是否有最新已签名的
            if (!judgeFileType(param.getOpinionFiles())) {
                //非手动上传的
                String fileIds = investigationSignService.retroSignature(data.getId(), param.getOpinionFiles(), "opinion_review");
                sysFileInfoService.setBiz(fileIds, param.getId(), "opinion_review");
            } else {
                //手动上传的
                sysFileInfoService.setBiz(param.getOpinionFiles(), param.getId(), "opinion_review");
            }
        }
        sysFileInfoService.removeBizBind(param.getId(), "eval_review");
        //判断是否有最新已签名的
        if (ObjectUtil.isNotEmpty(param.getEvaluationFiles())) {
            if (!judgeFileType(param.getEvaluationFiles())) {
                String fileIds = investigationSignService.retroSignature(data.getId(), param.getEvaluationFiles(), "eval_review");
                sysFileInfoService.setBiz(fileIds, param.getId(), "eval_review");
            } else {
                //手动上传的
                sysFileInfoService.setBiz(param.getEvaluationFiles(), param.getId(), "eval_review");
            }
        }
        if ("1".equals(param.getIsDraft())) {
            investigationInfoFlowService.draftTag(param.getId(), "PGZT05");
            return;
        }
        if ("1".equals(param.getApprovalResult())) {
            //通过 下一步 状态：待审批
            //待审批数据初始化
            InvestigationApproval approvalData = new InvestigationApproval();
            approvalData.setId(param.getId());
            approvalData.setDocNum(data.getDocNum());
            approvalData.setConclusion(data.getConclusion());
            approvalData.setParticular(data.getParticular());
            approvalData.setEntrustmentReceiveTime(param.getOtherInfo().getDate("entrustmentReceiveTime"));
            investigationApprovalService.saveOrUpdate(approvalData);
            HashMap<String, Object> vars = new HashMap<>();
            vars.put("result", 1);
            Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
            if (null != task) {
                taskService.complete(task.getId(), vars);
            }
            //更新流程记录
            LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                    .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                    .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                    .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                    .set(InvestigationInfoFlow::getMsg, param.getApprovalRemark())
                    .set(InvestigationInfoFlow::getSpTime, new Date())
                    .set(InvestigationInfoFlow::getType, 1)
                    .set(InvestigationInfoFlow::getFormVal, detail(param.getId(), null).toJSONString())
                    .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                    .eq(InvestigationInfoFlow::getStepCode, "PGZT05")
                    .eq(InvestigationInfoFlow::getType, 9);
            investigationInfoFlowService.update(flowLambdaUpdateWrapper);
            //更新基本信息状态为待审批
            this.lambdaUpdate()
                    .set(InvestigationInfo::getStatus, "PGZT06")
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
        } else {
            // 退回 状态：待初审/小组意见
            HashMap<String, Object> vars = new HashMap<>();
            vars.put("result", 2);
            Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
            if (null != task) {
                taskService.complete(task.getId(), vars);
            }
            //更新流程记录
            investigationInfoFlowService.pyBack(param);
            //更新基本信息状态为待初审/小组意见
            this.lambdaUpdate()
                    .set(InvestigationInfo::getStatus, "PGZT04")
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
        }

    }

    @Override
    @Transactional
    public void approval(InvestigationApprovalParam param) {
        InvestigationApproval data = new InvestigationApproval();
        BeanUtil.copyProperties(param, data, "evaluationFiles");
        investigationApprovalService.saveOrUpdate(data);
        this.lambdaUpdate().set(InvestigationInfo::getConclusion, param.getConclusion()).eq(InvestigationInfo::getId, param.getId()).update();
        if (ObjectUtil.isNotEmpty(param.getOpinionFiles())) {
            //删除之前的文书
            LambdaQueryWrapper<SysFileInfo> fileInfoWrapper = new LambdaQueryWrapper<>();
            fileInfoWrapper.eq(SysFileInfo::getBizId, param.getId());
            fileInfoWrapper.eq(SysFileInfo::getBizType, "opinion_approval");
            fileInfoWrapper.notIn(SysFileInfo::getId, param.getOpinionFiles().split(","));
            sysFileInfoService.remove(fileInfoWrapper);
            //附件： 集调查评估意见书
            //判断是否有最新已签名的
            if (!judgeFileType(param.getOpinionFiles())) {
                String fileIds = investigationSignService.retroSignature(data.getId(), param.getOpinionFiles(), "opinion_approval");
                sysFileInfoService.setBiz(fileIds, param.getId(), "opinion_approval");
            } else {
                //手动上传的
                sysFileInfoService.setBiz(param.getOpinionFiles(), param.getId(), "opinion_approval");
            }
        }
        sysFileInfoService.removeBizBind(param.getId(), "eval_approval");
        //判断是否有最新已签名的
        if (ObjectUtil.isNotEmpty(param.getEvaluationFiles())) {
            if (!judgeFileType(param.getEvaluationFiles())) {
                //非手动上传的
                String fileIds = investigationSignService.retroSignature(data.getId(), param.getEvaluationFiles(), "eval_approval");
                sysFileInfoService.setBiz(fileIds, param.getId(), "eval_approval");
            } else {
                //手动上传的
                sysFileInfoService.setBiz(param.getEvaluationFiles(), param.getId(), "eval_approval");
            }
        }
        if ("1".equals(param.getIsDraft())) {
            investigationInfoFlowService.draftTag(param.getId(), "PGZT06");
            //更新基本信息中的是否适宜社区矫正
            this.lambdaUpdate()
                    .set(InvestigationInfo::getConclusion, param.getConclusion())
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
            return;
        }
        if ("1".equals(param.getApprovalResult())) {
            //通过 下一步状态：待反馈
            //待反馈数据初始
            InvestigationFeedback feedback = new InvestigationFeedback();
            feedback.setId(param.getId());
            feedback.setInveDeptContactPsn(param.getInveDeptContactPsn());
            feedback.setInveDeptTel(param.getInveDeptTel());
            feedback.setDocNum(param.getDocNum());
            feedback.setConclusion(data.getConclusion());
            feedback.setEntrustmentReceiveTime(data.getEntrustmentReceiveTime());
            feedback.setEndTime(data.getEndTime());
            feedback.setParticular(data.getParticular());
            feedback.setResidenceCode(param.getOtherInfo().getString("residenceCode"));
            feedback.setResidence(param.getOtherInfo().getString("residence"));
            String[] jhrArr = investigationTranscriptService.getJhr(param.getOtherInfo().getJSONArray("transcripts"));
            if (null != jhrArr) {
                feedback.setGuardianName(jhrArr[0]);
                feedback.setGuardianRelationship(jhrArr[1]);
            }
            feedback.setFeedbackTo(param.getOtherInfo().getString("entrustmentDeptName"));
            //初始调查过程信息
            feedback.setProcessInfo(investigationTranscriptService.initProcessInfo(param.getOtherInfo().getJSONArray("transcripts"),
                    param.getOtherInfo().getString("correctionObjName"), param.getOtherInfo().getString("psnType")));
            investigationFeedbackService.saveOrUpdate(feedback);
            HashMap<String, Object> vars = new HashMap<>();
            vars.put("result", 1);
            Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
            if (null != task) {
                taskService.complete(task.getId(), vars);
            }
            //更新流程记录
            LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                    .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                    .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                    .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                    .set(InvestigationInfoFlow::getMsg, param.getApprovalRemark())
                    .set(InvestigationInfoFlow::getSpTime, new Date())
                    .set(InvestigationInfoFlow::getType, 1)
                    .set(InvestigationInfoFlow::getFormVal, detail(param.getId(), null).toJSONString())
                    .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                    .eq(InvestigationInfoFlow::getStepCode, "PGZT06")
                    .eq(InvestigationInfoFlow::getType, 9);
            investigationInfoFlowService.update(flowLambdaUpdateWrapper);
            //更新基本信息状态为待反馈
            this.lambdaUpdate()
                    .set(InvestigationInfo::getStatus, "PGZT07")
                    .set(InvestigationInfo::getConclusion, param.getConclusion())
                    .set(InvestigationInfo::getNoticeDocNum, data.getDocNum())
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
        } else {
            // 退回 状态：待初审/评议
            HashMap<String, Object> vars = new HashMap<>();
            vars.put("result", 2);
            Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
            if (null != task) {
                taskService.complete(task.getId(), vars);
            }
            //更新流程记录
            investigationInfoFlowService.dspBack(param);
            //更新基本信息状态为待初审/评议
            this.lambdaUpdate()
                    .set(InvestigationInfo::getStatus, "PGZT05")
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
        }

    }

    @Override
    @Transactional
    public void feedback(InvestigationFeedbackParam param) {
        InvestigationFeedback data = new InvestigationFeedback();
        BeanUtil.copyProperties(param, data);
        investigationFeedbackService.saveOrUpdate(data);
        if (ObjectUtil.isNotEmpty(param.getFeedbackFiles())) {
            List<SysFileInfo> list = sysFileInfoService.lambdaQuery().in(SysFileInfo::getId, Arrays.asList(param.getFeedbackFiles().split(","))).list();
            for (SysFileInfo file : list) {
                if (ObjectUtil.isNotEmpty(file.getBizId()) && !"feedback".equals(file.getBizType())) {
                    SysFileInfo newFileInfo = new SysFileInfo();
                    BeanUtil.copyProperties(file, newFileInfo);
                    newFileInfo.setId(null);
                    newFileInfo.setBizId(param.getId());
                    newFileInfo.setBizType("feedback");
                    sysFileInfoService.save(newFileInfo);
                } else {
                    // 反馈步骤新上传的附件 或者是默认初始化的调查评估意见书 和 调查评估表 或是已经暂存过的
                    file.setBizId(param.getId());
                    file.setBizType("feedback");
                    sysFileInfoService.updateById(file);
                }
            }
        }
        if ("1".equals(param.getIsDraft())) {
            investigationInfoFlowService.draftTag(param.getId(), "PGZT07");
            return;
        }
        InvestigationInfo investigationInfo = this.getById(param.getId());

        if ("1".equals(investigationInfo.getEntrustmentType())) {
            sendFeedback(param, data, investigationInfo);
        }

        // 状态：完成反馈，流程结束
        HashMap<String, Object> vars = new HashMap<>();
        vars.put("result", 1);
        Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(param.getId())).singleResult();
        if (null != task) {
            taskService.complete(task.getId(), vars);
        }
        //更新流程记录
        LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                .set(InvestigationInfoFlow::getSpTime, new Date())
                .set(InvestigationInfoFlow::getType, 1)
                .set(InvestigationInfoFlow::getFormVal, detail(param.getId(), null).toJSONString())
                .eq(InvestigationInfoFlow::getInvestigationInfoId, investigationInfo.getId())
                .eq(InvestigationInfoFlow::getStepCode, "PGZT07")
                .eq(InvestigationInfoFlow::getType, 9);
        investigationInfoFlowService.update(flowLambdaUpdateWrapper);
        //更新基本信息状态为已完结
        this.lambdaUpdate()
                .set(InvestigationInfo::getStatus, "PGZT08")
                .set(InvestigationInfo::getEndTime, data.getEndTime())
                .set(InvestigationInfo::getNoticeDocNum, data.getDocNum())
                .eq(InvestigationInfo::getId, param.getId())
                .update();
    }

    private void sendFeedback(InvestigationFeedbackParam param, InvestigationFeedback data, InvestigationInfo investigationInfo) {
        List<SysFileInfoVO> docList = sysFileInfoService.getDocList(param.getId(), "feedback");
        param.setResidenceCode(transToAreaIds(param.getResidenceCode()));
        investigationInfo.setResidenceCode(transToAreaCode(investigationInfo.getResidenceCode()));
        investigationInfo.setRegisteredAddressCode(transToAreaCode(investigationInfo.getRegisteredAddressCode()));
        if ("XTBH31001".equals(investigationInfo.getServerNum())) {
            if (!sendService.request31002_1(investigationInfo, data, docList)) {
                return;
            }
            sendService.request31002_2(investigationInfo, data, docList);
        }
        if ("XTBH31003".equals(investigationInfo.getServerNum())) {
            if (!sendService.request31004_1(investigationInfo, data, docList)) {
                return;
            }
            sendService.request31002_2(investigationInfo, data, docList);
        }
        if ("XTBH31005".equals(investigationInfo.getServerNum())) {
            if (!sendService.request31006_1(investigationInfo, data, docList)) {
                return;
            }
            sendService.request31002_2(investigationInfo, data, docList);
        }
        if ("XTBH31007".equals(investigationInfo.getServerNum())) {
            if (!sendService.request31007_1(investigationInfo, data, docList)) {
                return;
            }
        }
    }

    @Override
    public void receive(InvestigationInfoParam param) {
        InvestigationInfo old = this.lambdaQuery()
                .eq(InvestigationInfo::getReceiveDept, param.getReceiveDept())
                .eq(InvestigationInfo::getCertNum, param.getCertNum())
                .orderByDesc(InvestigationInfo::getEntrustmentReceiveTime)
                .last("limit 1").one();

        if (old != null) {
            //todo 自动退回
        }
        param.setStatus("PGZT01");
        param.setResidenceCode(transToAreaIds(param.getResidenceCode()));
        param.setRegisteredAddressCode(transToAreaIds(param.getRegisteredAddressCode()));
        if (ObjectUtil.isEmpty(param.getEntrustmentDept())) {
            param.setEntrustmentDept(param.getSendDept());
        }
        if (ObjectUtil.isEmpty(param.getEntrustmentDeptAddress())) {
            param.setEntrustmentDeptAddress(param.getSendDeptName());
        }

        InvestigationInfo info = new InvestigationInfo();
        BeanUtil.copyProperties(param, info);
        info.setCriminalChargeChinese(chargeInfoService.toChinese(param.getCriminalChargeList()));
        if (ObjectUtil.isNotEmpty(param.getExpeditedProcedure())) {
            info.setDayNum("1".equals(param.getExpeditedProcedure()) ? 5 : 10);
        }
        this.save(info);
        ywxtNoticeService.buildNoticeByOrgId(
                YwxtNoticeTypeEnum.INVEST_01,
                param.getReceiveDeptName() + "," + param.getEntrustmentReceiveTime() + "," + param.getSendDeptName() + "," + param.getCorrectionObjName(),
                param.getSendDeptName() + "," + param.getCorrectionObjName(),
                param.getReceiveDeptId());
        // 状态：待接收 接收一体化数据后，开启流程
        runtimeService.startProcessInstanceByKey("DCPG", info.getId());
        //初始化流程记录
        investigationInfoFlowService.initFlow(info.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(InvestigationInfoParam investigationInfoParam) {
        this.removeById(investigationInfoParam.getId());
    }

    @Override
    public JSONObject detail(String id, String approvalDeptId) {
        // String id = param.getId();
        InvestigationInfo data = this.getById(id);
        if (null == data) {
            //表示是新增
            data = new InvestigationInfo();
            data.setId(id);
            data.setEntrustmentCode(id);
            data.setEntrustmentReceiveTime(new Date());
            data.setEntrustmentType("2");
            data.setReceiveDeptId(approvalDeptId);
            return (JSONObject) JSONObject.toJSON(data);
        }
        JSONObject detail = (JSONObject) JSONObject.toJSON(data);
        if ("PGZT98".equals(data.getStatus())) {
            //退回的则查找附件
            detail.put("returnFiles", sysFileInfoService.getFileList(id, "LCZZ"));
        }
        detail.put("criminalCharge", JSON.parse(data.getCriminalCharge()));
        InvestigationGroup group = investigationGroupService.getById(id);
        if (group != null) {
            //文号拆3个
            if (ObjectUtil.isNotEmpty(group.getNoticeDocNum())) {
                // 正则匹配：任意字符 + "第" + 数字 + "号"
                Pattern pattern = Pattern.compile("(.+第)(\\d+)(号)");
                Matcher matcher = pattern.matcher(group.getNoticeDocNum());

                if (matcher.find()) {
                    group.setNoticeDocNumOne(matcher.group(1));
                    group.setNoticeDocNumTwo(matcher.group(2));
                    group.setNoticeDocNumThree("号");
                }
            }
            group.setNoticeDocList(sysFileInfoService.getFileList(id, "inve_notice"));
            JSONObject groupJson = (JSONObject) JSONObject.toJSON(group);
            groupJson.put("invePsnList", JSON.parse(group.getInvePsnList()));
            groupJson.put("returnFiles", sysFileInfoService.getFileList(id, "inve_return2"));
            groupJson.put("evaluationFiles", sysFileInfoService.getFileList(id, "eval_draft"));
            groupJson.put("pgqdFileList", sysFileInfoService.getFileList(id, "inve_pgqd"));
            groupJson.put("dctjFileList", sysFileInfoService.getFileList(id, "inve_dctj"));
            groupJson.put("jcyjFileList", sysFileInfoService.getFileList(id, "JCZZYJ"));
            groupJson.put("gayjFileList", sysFileInfoService.getFileList(id, "PCSYJ"));
            detail.put("groups", groupJson);
        }
        detail.put("docList", acceptCorrectionDocService.list(id));
        List<InvestigationTranscript> transcriptsList = investigationTranscriptService.lambdaQuery().eq(InvestigationTranscript::getPid, id).eq(InvestigationTranscript::getDeleted, 0).list();
        for (InvestigationTranscript investigationTranscript : transcriptsList) {
            if (null != investigationTranscript.getTag() && 2 == investigationTranscript.getTag()) {
                //手动上传的则查询笔录附件
                investigationTranscript.setBlFileList(sysFileInfoService.getFileList(id, "eval_bl"));
            }
        }
        if (1 == data.getTranscriptTag()) {
            detail.put("blList", sysFileInfoService.getFileList(id, "BLLX"));
        }
        detail.put("transcripts", transcriptsList);
        InvestigationDeliberation deliberation = investigationDeliberationService.getById(id);
        detail.put("deliberations", deliberation);
        if (null != deliberation) {
            deliberation.setOpinionFilesList(sysFileInfoService.getFileList(id, "opinion_deliberate"));
            deliberation.setEvaluationFiles(sysFileInfoService.getFileList(id, "eval_deliberate"));
        }
        InvestigationReview review = investigationReviewService.getById(id);
        detail.put("review", review);
        if (null != review) {
            review.setOpinionFilesList(sysFileInfoService.getFileList(id, "opinion_review"));
            review.setEvaluationFiles(sysFileInfoService.getFileList(id, "eval_review"));
        }
        InvestigationApproval approval = investigationApprovalService.getById(id);
        detail.put("approval", approval);
        if (null != approval) {
            if ("PGZT06".equals(data.getStatus())) {
                //状态是待审批的，则调查结束时间初始化为当前时间
                approval.setEndTime(new Date());
            }
            approval.setOpinionFilesList(sysFileInfoService.getFileList(id, "opinion_approval"));
            approval.setEvaluationFiles(sysFileInfoService.getFileList(id, "eval_approval"));
        }
        JSONObject feedback = (JSONObject) JSONObject.toJSON(investigationFeedbackService.getById(id));
        if (feedback != null) {
            LambdaQueryWrapper<SysFileInfo> fileWrapper = new LambdaQueryWrapper<>();
            fileWrapper.eq(SysFileInfo::getBizId, id);
            fileWrapper.eq(SysFileInfo::getBizType, "feedback");
            List<SysFileInfo> feedbackFileList = sysFileInfoService.list(fileWrapper);
            if (feedbackFileList.size() > 0) {
                //已有反馈附件这返回
                feedback.put("existsFiles", feedbackFileList);
            } else {
                //没有则表明是刚进入待反馈，默认初始化1份 意见书
                JSONArray existsFiles = new JSONArray();
                sysFileInfoService.lambdaQuery().eq(SysFileInfo::getBizId, id).eq(SysFileInfo::getBizType, "opinion_approval").list().forEach(item -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("id", item.getId());
                    jsonObject.put("fileOriginName", item.getFileOriginName());
                    jsonObject.put("filePath", OssSignedUrlUtil.generatePresignedUrlWithPublicDomain(item.getFilePath()));
                    existsFiles.add(jsonObject);
                });
                /*sysFileInfoService.lambdaQuery().eq(SysFileInfo::getBizId, id).eq(SysFileInfo::getBizType, "eval_approval").list().forEach(item -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("id", item.getId());
                    jsonObject.put("fileOriginName", item.getFileOriginName());
                    jsonObject.put("filePath", item.getFilePath());
                    existsFiles.add(jsonObject);
                });*/
                feedback.put("existsFiles", existsFiles);
            }
        }
        detail.put("feedback", feedback);
        return detail;
    }


    private String transToAreaIds(String areaCode) {
        List<String> areaIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(areaCode)) {
            AreaInfo areaInfo = areaInfoService.lambdaQuery().eq(AreaInfo::getAreacode, areaCode).last("limit 1").one();
            getParentArea(areaInfo, areaIds);
        }
        if (areaIds.size() > 0) {
            return String.join(",", ListUtil.reverse(areaIds));
        } else {
            return "";
        }
    }

    private String transToAreaCode(String areaIds) {
        if (ObjectUtil.isNotEmpty(areaIds)) {
            String[] areaCode = areaIds.split(",");
            return areaInfoService.getAreaCode(areaCode[areaCode.length - 1]);
        }
        return "";
    }

    private void getParentArea(AreaInfo areaInfo, List<String> areaIds) {
        if (areaInfo == null) {
            return;
        }
        areaIds.add(areaInfo.getAreaid());
        if (areaInfo.getArealevel() != 1) {
            AreaInfo parentArea = areaInfoService.getById(areaInfo.getParentareaid());
            getParentArea(parentArea, areaIds);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean backToOutSys(InvestigationInfoParam investigationInfoParam) {
        try {
            //更新基本信息状态为流程终止
            this.lambdaUpdate()
                    .set(InvestigationInfo::getStatus, "PGZT98")
                    .set(InvestigationInfo::getEndTime, new Date())
                    .eq(InvestigationInfo::getId, investigationInfoParam.getId())
                    .update();
            //调用工作流终止流程
            HashMap<String, Object> vars = new HashMap<>();
            vars.put("result", 3);
            Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(investigationInfoParam.getId())).singleResult();
            if (null != task) {
                taskService.complete(task.getId(), vars);
            }
            InvestigationInfo investigationInfo = this.getById(investigationInfoParam.getId());
            //先删除未开始的流程记录
            LambdaQueryWrapper<InvestigationInfoFlow> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(InvestigationInfoFlow::getInvestigationInfoId, investigationInfoParam.getId());
            queryWrapper.eq(InvestigationInfoFlow::getType, 9);
            investigationInfoFlowService.remove(queryWrapper);
            //添加流程终止记录
            InvestigationInfoFlow investigationInfoFlow = new InvestigationInfoFlow();
            investigationInfoFlow.setWtTime(investigationInfo.getEntrustmentTime());
            investigationInfoFlow.setGwDeptName(investigationInfoParam.getEntrustmentDeptName());
            investigationInfoFlow.setInvestigationInfoId(investigationInfoParam.getId());
            investigationInfoFlow.setNodeLevel(1);
            investigationInfoFlow.setType(0);
            investigationInfoFlow.setStepCode(investigationInfo.getStatus());
            investigationInfoFlow.setStepName(investigationInfoParam.getStepName());
            investigationInfoFlow.setDeptId(investigationInfoParam.getApprovalDeptId());
            investigationInfoFlow.setDeptName(investigationInfoParam.getApprovalDeptName());
            investigationInfoFlow.setUserId(investigationInfoParam.getApprovalPsnId());
            investigationInfoFlow.setUserName(investigationInfoParam.getApprovalPsn());
            investigationInfoFlow.setMsg(investigationInfoParam.getApprovalRemark());
            investigationInfoFlow.setSpTime(new Date());
            investigationInfoFlow.setFormVal(detail(investigationInfoParam.getId(), null).toJSONString());
            investigationInfoFlowService.save(investigationInfoFlow);
            //绑定流程终止附件 bizType=LCZZ
            sysFileInfoService.setBizList(investigationInfoParam.getReturnFiles(), investigationInfoParam.getId(), "LCZZ");
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("backToOutSys invest error: {}, {}", investigationInfoParam.getId(), e.getMessage());
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean stop(InvestigationInfoParam investigationInfoParam) {
        try {
            //调用工作流终止流程
            HashMap<String, Object> vars = new HashMap<>();
            vars.put("result", 3);
            Task task = taskService.createTaskQuery().processDefinitionKey("DCPG").processInstanceBusinessKey(String.valueOf(investigationInfoParam.getId())).singleResult();
            if (null != task) {
                taskService.complete(task.getId(), vars);
            }
            String stepCode = null;
            String stepName = null;
            InvestigationInfo investigationInfo = this.getById(investigationInfoParam.getId());
            //先删除未开始的流程记录
            LambdaQueryWrapper<InvestigationInfoFlow> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(InvestigationInfoFlow::getInvestigationInfoId, investigationInfoParam.getId());
            queryWrapper.eq(InvestigationInfoFlow::getType, 9);
            if ("PGZT03".equals(investigationInfo.getStatus())) {
                //如果终止的是调查评估阶段 则需细分到3个小阶段
                queryWrapper.orderByAsc(InvestigationInfoFlow::getSpTime);
                List<InvestigationInfoFlow> flowList = investigationInfoFlowService.list(queryWrapper);
                if (null != flowList && flowList.size() > 0) {
                    stepCode = flowList.get(0).getStepCode();
                    stepName = flowList.get(0).getStepName();
                }
            }
            investigationInfoFlowService.remove(queryWrapper);
            //添加流程终止记录
            InvestigationInfoFlow investigationInfoFlow = new InvestigationInfoFlow();
            investigationInfoFlow.setWtTime(investigationInfo.getEntrustmentTime());
            investigationInfoFlow.setGwDeptName(investigationInfoParam.getEntrustmentDeptName());
            investigationInfoFlow.setInvestigationInfoId(investigationInfoParam.getId());
            investigationInfoFlow.setNodeLevel(changeType(investigationInfo.getStatus()));
            investigationInfoFlow.setType(2);
            investigationInfoFlow.setStepCode(null != stepCode ? stepCode : investigationInfo.getStatus());
            investigationInfoFlow.setStepName(null != stepName ? stepName : investigationInfoParam.getStepName());
            investigationInfoFlow.setDeptId(investigationInfoParam.getApprovalDeptId());
            investigationInfoFlow.setDeptName(investigationInfoParam.getApprovalDeptName());
            investigationInfoFlow.setUserId(investigationInfoParam.getApprovalPsnId());
            investigationInfoFlow.setUserName(investigationInfoParam.getApprovalPsn());
            investigationInfoFlow.setMsg(investigationInfoParam.getApprovalRemark());
            investigationInfoFlow.setSpTime(new Date());
            investigationInfoFlow.setFormVal(detail(investigationInfoParam.getId(), null).toJSONString());
            investigationInfoFlowService.save(investigationInfoFlow);
            //绑定流程终止附件 bizType=LCZZ
            sysFileInfoService.setBizList(investigationInfoParam.getReturnFiles(), investigationInfoParam.getId(), "LCZZ");
            //更新基本信息状态为流程终止
            this.lambdaUpdate()
                    .set(InvestigationInfo::getStatus, "PGZT99")
                    .set(InvestigationInfo::getEndTime, new Date())
                    .eq(InvestigationInfo::getId, investigationInfoParam.getId())
                    .update();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("stop invest error: {}, {}", investigationInfoParam.getId(), e.getMessage());
            return false;
        }
    }

    private static final int[] RESULT_MAP = {0, 1, 1, 2, 3, 3, 3, 4};

    /**
     * 根据评估状态转换 NodeLevel
     *
     * @param status
     * @return
     */
    private int changeType(String status) {
        //注意： 字典值：PGZT 有变化时需判断该方法是否需要调整
        int num = Integer.parseInt(status.replace("PGZT", ""));
        if (num >= 1 && num <= 7) {
            return RESULT_MAP[num];
        }
        return 0;
    }

    @Override
    public void uptDayNum() {
        List<InvestigationInfo> list = this.list(new LambdaQueryWrapper<InvestigationInfo>()
                .eq(InvestigationInfo::getDeleted, 0)
                .notIn(InvestigationInfo::getStatus, "PGZT08,PGZT98,PGZT99".split(","))
                .isNotNull(InvestigationInfo::getDayNum)
                .isNotNull(InvestigationInfo::getEntrustmentTime));
        for (InvestigationInfo investigationInfo : list) {
            this.lambdaUpdate()
                    .set(InvestigationInfo::getDayNum, sysHolidayService.countDay(investigationInfo.getEntrustmentReceiveTime(),
                            "1".equals(investigationInfo.getExpeditedProcedure()) ? 5 : 10))
                    .eq(InvestigationInfo::getId, investigationInfo.getId())
                    .update();
        }
    }
}
