package com.concise.gen.acceptcorrectiondoc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.*;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.config.FileConfig;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.param.SysFileInfoVO;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.file.util.OssBootUtil;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.HttpServletUtil;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.enums.AcceptCorrectionDocExceptionEnum;
import com.concise.gen.acceptcorrectiondoc.mapper.AcceptCorrectionDocMapper;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.utils.WdInterface;
import com.concise.gen.utils.dto.PersonInfoDocDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 矫正对象法律文书信息信息接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:33
 */
@Service
public class AcceptCorrectionDocServiceImpl extends ServiceImpl<AcceptCorrectionDocMapper, AcceptCorrectionDoc> implements AcceptCorrectionDocService {
    @Value("${sqjzOss.endpoint}")
    private String endPoint;
    @Value("${sqjzOss.accessKey}")
    private String accessKeyId;
    @Value("${sqjzOss.secretKey}")
    private String accessKeySecret;
    @Value("${sqjzOss.bucketName}")
    private String bucketName;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Override
    public PageResult<AcceptCorrectionDoc> page(AcceptCorrectionDocParam acceptCorrectionDocParam) {
        QueryWrapper<AcceptCorrectionDoc> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(acceptCorrectionDocParam)) {

            queryWrapper.lambda().eq(AcceptCorrectionDoc::getContactId, acceptCorrectionDocParam.getContactId());
            // 根据文书名称 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionDocParam.getWs())) {
                queryWrapper.lambda().eq(AcceptCorrectionDoc::getWs, acceptCorrectionDocParam.getWs());
            }
            // 根据文书代码 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionDocParam.getWsdm())) {
                queryWrapper.lambda().eq(AcceptCorrectionDoc::getWsdm, acceptCorrectionDocParam.getWsdm());
            }
            // 根据URI 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionDocParam.getUri())) {
                queryWrapper.lambda().eq(AcceptCorrectionDoc::getUri, acceptCorrectionDocParam.getUri());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptCorrectionDoc> list(String contactId) {
        QueryWrapper<AcceptCorrectionDoc> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AcceptCorrectionDoc::getContactId, contactId);
        return this.list(queryWrapper);
    }

    @Override
    public List<SysFileInfoVO> getSysFileInfoParamList(String taskId) {
        List<SysFileInfoVO> fileInfoList= new ArrayList<>();
        List<AcceptCorrectionDoc> docs = this.lambdaQuery().eq(AcceptCorrectionDoc::getContactId,taskId).list();
        for (AcceptCorrectionDoc doc : docs) {
            if (ObjectUtil.isNotEmpty(doc.getOssUrl())) {
                SysFileInfoVO p = new SysFileInfoVO();
                p.setUid(doc.getId());
                p.setName(doc.getWs());
                p.setUrl(doc.getSignedUrl());
                fileInfoList.add(p);
            }
        }
        return fileInfoList;
    }

    @Override
    public List<SysFileInfoVO> getSysFileInfoList(String id, String serverNum) {
        return this.lambdaQuery()
                .eq(AcceptCorrectionDoc::getContactId, id).eq(AcceptCorrectionDoc::getXtbh, serverNum).list()
                .stream().map(doc -> new SysFileInfoVO(sysFileInfoService.getById(doc.getFileId()))).collect(Collectors.toList());
    }

    @Override
    public void add(AcceptCorrectionDocParam acceptCorrectionDocParam) {
        AcceptCorrectionDoc acceptCorrectionDoc = new AcceptCorrectionDoc();
        BeanUtil.copyProperties(acceptCorrectionDocParam, acceptCorrectionDoc);
        this.save(acceptCorrectionDoc);
    }

    @Override
    public void addBatch(List<AcceptCorrectionDocParam> acceptCorrectionDocParam) {
        acceptCorrectionDocParam.forEach(this::add);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AcceptCorrectionDocParam acceptCorrectionDocParam) {
        this.removeById(acceptCorrectionDocParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AcceptCorrectionDocParam acceptCorrectionDocParam) {
        AcceptCorrectionDoc acceptCorrectionDoc = this.queryAcceptCorrectionDoc(acceptCorrectionDocParam);
        BeanUtil.copyProperties(acceptCorrectionDocParam, acceptCorrectionDoc);
        this.updateById(acceptCorrectionDoc);
    }

    @Override
    public AcceptCorrectionDoc detail(AcceptCorrectionDocParam acceptCorrectionDocParam) {
        return this.queryAcceptCorrectionDoc(acceptCorrectionDocParam);
    }

    @Override
    public AcceptCorrectionDoc reLoadFileFromOss(String id) {
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);
        AcceptCorrectionDoc doc = this.getById(id);
        if (doc == null) {
            return null;
        }
        try {
            String fileName = doc.getId();
            int beginIndex = doc.getUri().lastIndexOf(".");
            if (beginIndex>=0) {
                fileName += doc.getUri().substring(beginIndex);
            }
            File file = OssBootUtil.download(FileConfig.DEFAULT_TEMP + File.separator, fileName,ossClient,bucketName,doc.getUri());
            SysFileInfo fileInfo = sysFileInfoService.uploadFileOss(file, id);
            if (fileInfo != null) {
                doc.setFileId(fileInfo.getId());
                doc.setOssUrl(fileInfo.getFilePath());
            }
        }catch (Exception e) {
            doc.setOssUrl(e.getMessage());
        }finally {
            this.updateById(doc);
        }
        return doc;
    }

    @Override
    public List<AcceptCorrectionDoc> reLoadFileListFromOss(String contactId) {
        List<AcceptCorrectionDoc> list = this.lambdaQuery().eq(AcceptCorrectionDoc::getContactId, contactId).list();
        if (list.size() == 0) {
            return null;
        }
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);
        for (AcceptCorrectionDoc doc : list) {
            try {
                String fileName = doc.getId();
                int beginIndex = doc.getUri().lastIndexOf(".");
                if (beginIndex>=0) {
                    fileName += doc.getUri().substring(beginIndex);
                }
                File file = OssBootUtil.download(FileConfig.DEFAULT_TEMP + File.separator, fileName,ossClient,bucketName,doc.getUri());
                SysFileInfo fileInfo = sysFileInfoService.uploadFileOss(file, doc.getId());
                if (fileInfo != null) {
                    doc.setFileId(fileInfo.getId());
                    doc.setOssUrl(fileInfo.getFilePath());
                }
            }catch (OSSException e) {
                doc.setOssUrl(e.getErrorCode());
            }finally {
                this.updateById(doc);
            }
        }
        return list;
    }

    @Override
    public void sendDocToWd(String contactId) {
        List<AcceptCorrectionDoc> list = this.lambdaQuery().eq(AcceptCorrectionDoc::getContactId, contactId).list();
        if (list.size() == 0) {
            return;
        }
        send(list, contactId);
    }

    private void send(List<AcceptCorrectionDoc> list, String pid) {
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);
        for (AcceptCorrectionDoc doc : list) {
            PersonInfoDocDTO docDTO = new PersonInfoDocDTO();
            String uuid = IdUtil.fastSimpleUUID();
            docDTO.setId(uuid);
            docDTO.setPid(pid);
            docDTO.setName(doc.getWs());
            docDTO.setReceivedDate(DateUtil.today());
            WdInterface.addPersonInfoDoc(docDTO);

            // 如果有文书oss地址，下载转base64并上传到万达
            if (ObjectUtil.isNotEmpty(doc.getUri())) {
                try {
                    PersonInfoDocDTO personInfoDocDTO = new PersonInfoDocDTO();
                    personInfoDocDTO.setId(uuid);
                    String base64 = downloadAndConvertToBase64(doc, ossClient, bucketName);
                    if (base64 != null) {
                        String[] stringArray = new String[]{base64};
                        personInfoDocDTO.setFlws(stringArray);
                        WdInterface.addPersonInfoUploadDoc(personInfoDocDTO);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public void sendDocToWd(String contactId, String psnId) {
        List<AcceptCorrectionDoc> list = this.lambdaQuery().eq(AcceptCorrectionDoc::getContactId, contactId).list();
        if (list.size() == 0) {
            return;
        }
        send(list, psnId);
    }

    @Override
    public void batchDownLoad(String contactId, String xm) {
        List<AcceptCorrectionDoc> list = this.lambdaQuery().eq(AcceptCorrectionDoc::getContactId, contactId).list();
        if (list.size() == 0) {
            return;
        }

        HttpServletResponse response = HttpServletUtil.getResponse();
        response.setCharacterEncoding(CharsetUtil.UTF_8);
        response.setContentType(MediaType.MULTIPART_FORM_DATA_VALUE);
        response.setHeader("Content-Disposition", "attachment; filename=\"" + URLUtil.encode(xm + "法律文书.zip") + "\"");
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);
        try {
            ZipOutputStream zos = new ZipOutputStream(response.getOutputStream());
            int sortNum = 1;
            for (AcceptCorrectionDoc doc : list) {
                InputStream inputStream = null;
                try {
                    inputStream = OssBootUtil.getObject(ossClient, bucketName, doc.getUri());
                    if (inputStream != null) {
                        zos.putNextEntry(new ZipEntry(sortNum+"_"+doc.getWs()));
                        IoUtil.copy(inputStream, zos);
                    }
                } catch (Exception e) {
                    //e.printStackTrace();
                } finally {
                    IoUtil.close(inputStream);
                    zos.closeEntry();
                    sortNum++;
                }
            }
            zos.close();
        } catch ( Exception e) {
            e.printStackTrace();
        } finally {
            //关闭流
            try {
                response.getOutputStream().flush();
                response.getOutputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    private static String downloadAndConvertToBase64(AcceptCorrectionDoc doc, OSSClient client,String bucketName) throws IOException {

        InputStream in = OssBootUtil.getObject(client, bucketName, doc.getUri());
        if (in == null) {
            return null;
        }
        String suffix = StrUtil.subAfter(doc.getUri(), SymbolConstant.PERIOD, true);
        try (
                BufferedInputStream inputStream = new BufferedInputStream(in);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream()
        ) {
            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            byte[] data = outputStream.toByteArray();

            // 将文件数据转换为Base64字符串，并添加文件格式信息
            return "data:" + getMimeType(suffix) + ";base64," + Base64.getEncoder().encodeToString(data);
        }
    }

    private static String getMimeType(String fileExtension) {
        // 根据文件后缀返回相应的MIME类型，这里只是一个简单的示例，实际上你可能需要一个更完整的映射表
        switch (fileExtension.toLowerCase()) {
            case "pdf":
                return "application/pdf";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "doc":
                return "application/msword";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 获取矫正对象法律文书信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    private AcceptCorrectionDoc queryAcceptCorrectionDoc(AcceptCorrectionDocParam acceptCorrectionDocParam) {
        AcceptCorrectionDoc acceptCorrectionDoc = this.getById(acceptCorrectionDocParam.getId());
        if (ObjectUtil.isNull(acceptCorrectionDoc)) {
            throw new ServiceException(AcceptCorrectionDocExceptionEnum.NOT_EXIST);
        }
        return acceptCorrectionDoc;
    }
}
