package com.concise.sys.modular.timer.tasks;

import com.concise.core.timer.TimerTaskRunner;
import com.concise.gen.investigation.service.InvestigationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class InvestDayNumTaskRunner implements TimerTaskRunner {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private InvestigationInfoService investigationInfoService;

    @Value("${scheduleEnable}")
    private boolean scheduleEnable;
    /**
     * 每天凌晨更新剩余天数
     */
    @Override
    public void action() {
        if (scheduleEnable) {
            String flag = stringRedisTemplate.opsForValue().get("Job:InvestDayNum");
            if (null != flag && "executeing".equals(flag)) {
                log.info("=======InvestDayNumTaskRunner本次已在执行=======");
                return;
            }
            stringRedisTemplate.opsForValue().set("Job:InvestDayNum", "executeing", 60, TimeUnit.MINUTES);
            log.info("===InvestDayNumTaskRunner start===");
            investigationInfoService.uptDayNum();
            log.info("===InvestDayNumTaskRunner end===");
        }
    }
}
